{"key": "plugin_content_manager_configuration_content_types::api::document.document", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "title", "defaultSortBy": "title", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "title": {"edit": {"label": "<PERSON><PERSON><PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "title", "searchable": true, "sortable": true}}, "slug": {"edit": {"label": "Slug", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "slug", "searchable": true, "sortable": true}}, "description": {"edit": {"label": "<PERSON><PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "description", "searchable": true, "sortable": true}}, "documentCategory": {"edit": {"label": "Kategória", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "title"}, "list": {"label": "documentCategory", "searchable": true, "sortable": true}}, "files": {"edit": {"label": "Súbory", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "files", "searchable": false, "sortable": false}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}, "createdBy": {"edit": {"label": "created<PERSON>y", "description": "", "placeholder": "", "visible": false, "editable": true, "mainField": "firstname"}, "list": {"label": "created<PERSON>y", "searchable": true, "sortable": true}}, "updatedBy": {"edit": {"label": "updatedBy", "description": "", "placeholder": "", "visible": false, "editable": true, "mainField": "firstname"}, "list": {"label": "updatedBy", "searchable": true, "sortable": true}}}, "layouts": {"edit": [[{"name": "title", "size": 12}], [{"name": "slug", "size": 12}], [{"name": "files", "size": 6}, {"name": "documentCategory", "size": 6}], [{"name": "description", "size": 12}]], "list": ["id", "title", "slug", "createdAt"]}, "uid": "api::document.document"}, "type": "object", "environment": null, "tag": null}