fragment ParentPage on Page {
  slug
  locale
  title
}

fragment PageParentPages on PageEntity {
  attributes {
    ...ParentPage
    parentPage {
      data {
        attributes {
          ...ParentPage
          parentPage {
            data {
              attributes {
                ...ParentPage
                parentPage {
                  data {
                    attributes {
                      ...ParentPage
                      parentPage {
                        data {
                          attributes {
                            ...ParentPage
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

fragment Localization on PageRelationResponseCollection {
  data {
    attributes {
      slug
      locale
    }
  }
}

fragment PageSlugEntity on PageEntity {
  id
  attributes {
    slug
    title
    locale
  }
}

fragment PageEntity on PageEntity {
  ...PageSlugEntity
  attributes {
    alias
    subtext
    pageColor
    metaDiscription
    keywords
    pageBackgroundImage {
      data {
        ...UploadImageSrcEntity
      }
    }
    headerLinks {
      ...CommonLink
    }
    sections {
      ...Sections
    }
    localizations {
      ...Localization
    }
    pageHeaderSections {
      ...PageHeaderSections
    }
    pageCategory {
      data {
        id
        attributes {
          title
          color
        }
      }
    }
    relatedContents {
      data {
        ...TagEntity
      }
    }
  }
  ...PageParentPages
}

query PagesStaticPaths {
  pages(pagination: { limit: -1 }) {
    data {
      id
      attributes {
        slug
      }
    }
  }
}

query PageBySlug($slug: String!, $locale: I18NLocaleCode!) {
  pages(filters: { slug: { eq: $slug } }, locale: $locale) {
    data {
      ...PageEntity
    }
  }
}

# Based on OLO: https://github.com/bratislava/olo.sk/blob/master/next/src/services/graphql/queries/pages.gql
query PageRedirectByAlias($alias: String!, $locale: I18NLocaleCode!) {
  pages(filters: { alias: { eq: $alias } }, locale: $locale) {
    data {
      ...PageSlugEntity
    }
  }
  articles(filters: { alias: { eq: $alias } }, locale: $locale) {
    data {
      ...ArticleSlugEntity
    }
  }
}

query Dev_AllPages(
  $sort: [String]
  $limit: Int
  $start: Int
  $filters: PageFiltersInput
  $locale: I18NLocaleCode
) {
  pages(
    sort: $sort
    pagination: { limit: $limit, start: $start }
    filters: $filters
    locale: $locale
  ) {
    data {
      ...PageEntity
    }
  }
}
