"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/page-contents/HomepageContent.tsx":
/*!**********************************************************!*\
  !*** ./src/components/page-contents/HomepageContent.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @tanstack/react-query */ \"./node_modules/@tanstack/react-query/build/modern/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_components_common_Waves_Waves__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/components/common/Waves/Waves */ \"./src/components/common/Waves/Waves.tsx\");\n/* harmony import */ var _src_components_providers_HomepageContextProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/components/providers/HomepageContextProvider */ \"./src/components/providers/HomepageContextProvider.tsx\");\n/* harmony import */ var _src_components_sections_homepage_sections_InbaHomepageSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/src/components/sections/homepage-sections/InbaHomepageSection */ \"./src/components/sections/homepage-sections/InbaHomepageSection.tsx\");\n/* harmony import */ var _src_components_sections_homepage_sections_NewsAndInfoHomepageSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/src/components/sections/homepage-sections/NewsAndInfoHomepageSection */ \"./src/components/sections/homepage-sections/NewsAndInfoHomepageSection.tsx\");\n/* harmony import */ var _src_components_sections_homepage_sections_TopServicesHomepageSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/src/components/sections/homepage-sections/TopServicesHomepageSection */ \"./src/components/sections/homepage-sections/TopServicesHomepageSection.tsx\");\n/* harmony import */ var _src_components_sections_homepage_sections_WelcomeHomepageSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/src/components/sections/homepage-sections/WelcomeHomepageSection */ \"./src/components/sections/homepage-sections/WelcomeHomepageSection.tsx\");\n/* harmony import */ var _src_components_sections_TootootEventsSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/src/components/sections/TootootEventsSection */ \"./src/components/sections/TootootEventsSection.tsx\");\n/* harmony import */ var _src_services_graphql_gql__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/src/services/graphql/gql */ \"./src/services/graphql/gql.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst HomepageContent = ()=>{\n    var _homepage_attributes;\n    _s();\n    const { homepage } = (0,_src_components_providers_HomepageContextProvider__WEBPACK_IMPORTED_MODULE_3__.useHomepageContext)();\n    const { data: allPagesData } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useQuery)({\n        queryKey: [\n            \"all pages\"\n        ],\n        queryFn: ()=>_src_services_graphql_gql__WEBPACK_IMPORTED_MODULE_9__.client.Dev_AllPages({\n                locale: \"all\",\n                limit: -1\n            })\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            \"Uiii \",\n            JSON.stringify(allPagesData),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_sections_homepage_sections_WelcomeHomepageSection__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\components\\\\page-contents\\\\HomepageContent.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_common_Waves_Waves__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                waveColor: \"var(--color-grey-50)\",\n                wavePosition: \"top\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\components\\\\page-contents\\\\HomepageContent.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_sections_homepage_sections_NewsAndInfoHomepageSection__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\components\\\\page-contents\\\\HomepageContent.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_common_Waves_Waves__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                wavePosition: \"bottom\",\n                waveColor: \"var(--color-grey-50)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\components\\\\page-contents\\\\HomepageContent.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            (homepage === null || homepage === void 0 ? void 0 : (_homepage_attributes = homepage.attributes) === null || _homepage_attributes === void 0 ? void 0 : _homepage_attributes.eventsSection) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_sections_TootootEventsSection__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                section: homepage.attributes.eventsSection\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\components\\\\page-contents\\\\HomepageContent.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, undefined) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_common_Waves_Waves__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                waveColor: \"var(--color-category-200)\",\n                wavePosition: \"top\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\components\\\\page-contents\\\\HomepageContent.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_sections_homepage_sections_TopServicesHomepageSection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\components\\\\page-contents\\\\HomepageContent.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_common_Waves_Waves__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                waveColor: \"var(--color-category-200)\",\n                wavePosition: \"bottom\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\components\\\\page-contents\\\\HomepageContent.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_sections_homepage_sections_InbaHomepageSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\components\\\\page-contents\\\\HomepageContent.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(HomepageContent, \"YFlKGdGwuUi9FEwuGeMnTLOW9d0=\", false, function() {\n    return [\n        _src_components_providers_HomepageContextProvider__WEBPACK_IMPORTED_MODULE_3__.useHomepageContext,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useQuery\n    ];\n});\n_c = HomepageContent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomepageContent);\nvar _c;\n$RefreshReg$(_c, \"HomepageContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/page-contents/HomepageContent.tsx\n"));

/***/ })

});