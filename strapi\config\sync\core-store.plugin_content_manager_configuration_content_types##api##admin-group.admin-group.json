{"key": "plugin_content_manager_configuration_content_types::api::admin-group.admin-group", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "title", "defaultSortBy": "title", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "title": {"edit": {"label": "title", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "title", "searchable": true, "sortable": true}}, "adminGroupId": {"edit": {"label": "adminGroupId", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "adminGroupId", "searchable": true, "sortable": true}}, "articles": {"edit": {"label": "articles", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "title"}, "list": {"label": "articles", "searchable": false, "sortable": false}}, "pages": {"edit": {"label": "pages", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "title"}, "list": {"label": "pages", "searchable": false, "sortable": false}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}, "createdBy": {"edit": {"label": "created<PERSON>y", "description": "", "placeholder": "", "visible": false, "editable": true, "mainField": "firstname"}, "list": {"label": "created<PERSON>y", "searchable": true, "sortable": true}}, "updatedBy": {"edit": {"label": "updatedBy", "description": "", "placeholder": "", "visible": false, "editable": true, "mainField": "firstname"}, "list": {"label": "updatedBy", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "title", "adminGroupId", "articles"], "edit": [[{"name": "title", "size": 6}, {"name": "adminGroupId", "size": 6}], [{"name": "articles", "size": 6}, {"name": "pages", "size": 6}]]}, "uid": "api::admin-group.admin-group"}, "type": "object", "environment": null, "tag": null}