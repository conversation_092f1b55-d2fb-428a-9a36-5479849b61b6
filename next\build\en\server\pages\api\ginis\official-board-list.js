"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/ginis/official-board-list";
exports.ids = ["pages/api/ginis/official-board-list"];
exports.modules = {

/***/ "@bratislava/ginis-sdk":
/*!****************************************!*\
  !*** external "@bratislava/ginis-sdk" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@bratislava/ginis-sdk");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

module.exports = import("axios");;

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fginis%2Fofficial-board-list&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cginis%5Cofficial-board-list.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fginis%2Fofficial-board-list&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cginis%5Cofficial-board-list.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_ginis_official_board_list_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\ginis\\official-board-list.ts */ \"(api)/./src/pages/api/ginis/official-board-list.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_api_ginis_official_board_list_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_src_pages_api_ginis_official_board_list_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_ginis_official_board_list_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_ginis_official_board_list_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/ginis/official-board-list\",\n        pathname: \"/api/ginis/official-board-list\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_ginis_official_board_list_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fginis%2Fofficial-board-list&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cginis%5Cofficial-board-list.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/ginis/official-board-list.ts":
/*!****************************************************!*\
  !*** ./src/pages/api/ginis/official-board-list.ts ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _src_services_ginis_fetchers_officialBoardListFetcher__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/src/services/ginis/fetchers/officialBoardListFetcher */ \"(api)/./src/services/ginis/fetchers/officialBoardListFetcher.ts\");\n/* harmony import */ var _src_services_ginis_mocks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/src/services/ginis/mocks */ \"(api)/./src/services/ginis/mocks.ts\");\n/* harmony import */ var _src_services_ginis_server_getOfficialBoardParsedList__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/src/services/ginis/server/getOfficialBoardParsedList */ \"(api)/./src/services/ginis/server/getOfficialBoardParsedList.ts\");\n/* harmony import */ var _src_services_ginis_utils_shouldMockGinis__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/services/ginis/utils/shouldMockGinis */ \"(api)/./src/services/ginis/utils/shouldMockGinis.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_services_ginis_fetchers_officialBoardListFetcher__WEBPACK_IMPORTED_MODULE_0__]);\n_src_services_ginis_fetchers_officialBoardListFetcher__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst handler = async (req, res)=>{\n    const { search: searchParam, pageSize: pageSizeParam, page: pageParam, publicationState: publicationStateParam, categoryId: categoryIdParam } = req.query;\n    // TODO parse query params in cleaner way\n    const search = typeof searchParam === \"string\" ? searchParam : searchParam?.[0] ?? _src_services_ginis_fetchers_officialBoardListFetcher__WEBPACK_IMPORTED_MODULE_0__.officialBoardListDefaultFilters.search;\n    const pageSize = typeof pageSizeParam === \"string\" ? parseInt(pageSizeParam, 10) : _src_services_ginis_fetchers_officialBoardListFetcher__WEBPACK_IMPORTED_MODULE_0__.officialBoardListDefaultFilters.pageSize;\n    const page = typeof pageParam === \"string\" ? parseInt(pageParam, 10) : _src_services_ginis_fetchers_officialBoardListFetcher__WEBPACK_IMPORTED_MODULE_0__.officialBoardListDefaultFilters.page;\n    const publicationState = typeof publicationStateParam === \"string\" ? publicationStateParam : publicationStateParam?.[0];\n    const categoryId = typeof categoryIdParam === \"string\" ? categoryIdParam : categoryIdParam?.[0] ?? \"\";\n    let result = [];\n    try {\n        result = (0,_src_services_ginis_utils_shouldMockGinis__WEBPACK_IMPORTED_MODULE_3__.shouldMockGinis)() ? _src_services_ginis_mocks__WEBPACK_IMPORTED_MODULE_1__.mockedParsedDocuments : await (0,_src_services_ginis_server_getOfficialBoardParsedList__WEBPACK_IMPORTED_MODULE_2__.getOfficialBoardParsedList)({\n            searchQuery: search,\n            publicationState,\n            categoryId\n        });\n    } catch (error) {\n        // TODO handle error\n        // eslint-disable-next-line no-console\n        console.log(error);\n    }\n    // Convert page and pageSize to zero-based index\n    const start = (page - 1) * pageSize;\n    const end = start + pageSize;\n    return res.json({\n        // TODO remove this hacky solution\n        // ignoring negative pageSize to be able to return all results for old approach in OfficialBoardSection\n        items: pageSize < 0 ? result : result.slice(start, end),\n        totalItems: result.length\n    });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (handler);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/ginis/official-board-list.ts\n");

/***/ }),

/***/ "(api)/./src/services/ginis/fetchers/officialBoardListFetcher.ts":
/*!*****************************************************************!*\
  !*** ./src/services/ginis/fetchers/officialBoardListFetcher.ts ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getOfficialBoardListQueryKey: () => (/* binding */ getOfficialBoardListQueryKey),\n/* harmony export */   officialBoardListDefaultFilters: () => (/* binding */ officialBoardListDefaultFilters),\n/* harmony export */   officialBoardListFetcher: () => (/* binding */ officialBoardListFetcher)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__]);\naxios__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst officialBoardListDefaultFilters = {\n    search: \"\",\n    pageSize: 10,\n    page: 1,\n    publicationState: \"vyveseno\"\n};\nconst getOfficialBoardListQueryKey = (filters)=>[\n        \"Search\",\n        \"OfficialBoardList\",\n        filters\n    ];\nconst officialBoardListFetcher = async (filters)=>{\n    return axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/api/ginis/official-board-list?${[\n        filters.search ? `search=${filters.search}` : \"\",\n        filters.pageSize ? `pageSize=${filters.pageSize.toString()}` : \"\",\n        filters.page ? `page=${filters.page.toString()}` : \"\",\n        filters.publicationState ? `publicationState=${filters.publicationState}` : \"\",\n        filters.categoryId ? `categoryId=${filters.categoryId}` : \"\"\n    ].filter(Boolean).join(\"&\")}`);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/services/ginis/fetchers/officialBoardListFetcher.ts\n");

/***/ }),

/***/ "(api)/./src/services/ginis/ginis.ts":
/*!*************************************!*\
  !*** ./src/services/ginis/ginis.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ginis: () => (/* binding */ ginis)\n/* harmony export */ });\n/* harmony import */ var _bratislava_ginis_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @bratislava/ginis-sdk */ \"@bratislava/ginis-sdk\");\n/* harmony import */ var _bratislava_ginis_sdk__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_bratislava_ginis_sdk__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ginis = new _bratislava_ginis_sdk__WEBPACK_IMPORTED_MODULE_0__.Ginis({\n    // connect to any subset of services needed, all the urls are optional but requests to services missing urls will fail\n    urls: {\n        ude: process.env.GINIS_HOST_UDE ?? \"\"\n    },\n    // credentials\n    username: process.env.GINIS_USERNAME ?? \"\",\n    password: process.env.GINIS_PASSWORD ?? \"\",\n    // if debug === true prints all the requests and responses into console\n    // warning - these logs WILL include credentials!\n    debug: false\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvc2VydmljZXMvZ2luaXMvZ2luaXMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBRXRDLE1BQU1DLFFBQVEsSUFBSUQsd0RBQUtBLENBQUM7SUFDN0Isc0hBQXNIO0lBQ3RIRSxNQUFNO1FBQ0pDLEtBQUtDLFFBQVFDLEdBQUcsQ0FBQ0MsY0FBYyxJQUFJO0lBQ3JDO0lBQ0EsY0FBYztJQUNkQyxVQUFVSCxRQUFRQyxHQUFHLENBQUNHLGNBQWMsSUFBSTtJQUN4Q0MsVUFBVUwsUUFBUUMsR0FBRyxDQUFDSyxjQUFjLElBQUk7SUFDeEMsdUVBQXVFO0lBQ3ZFLGlEQUFpRDtJQUNqREMsT0FBTztBQUNULEdBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9icmF0aXNsYXZhLW5leHQvLi9zcmMvc2VydmljZXMvZ2luaXMvZ2luaXMudHM/MWM3YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBHaW5pcyB9IGZyb20gJ0BicmF0aXNsYXZhL2dpbmlzLXNkaydcblxuZXhwb3J0IGNvbnN0IGdpbmlzID0gbmV3IEdpbmlzKHtcbiAgLy8gY29ubmVjdCB0byBhbnkgc3Vic2V0IG9mIHNlcnZpY2VzIG5lZWRlZCwgYWxsIHRoZSB1cmxzIGFyZSBvcHRpb25hbCBidXQgcmVxdWVzdHMgdG8gc2VydmljZXMgbWlzc2luZyB1cmxzIHdpbGwgZmFpbFxuICB1cmxzOiB7XG4gICAgdWRlOiBwcm9jZXNzLmVudi5HSU5JU19IT1NUX1VERSA/PyAnJyxcbiAgfSxcbiAgLy8gY3JlZGVudGlhbHNcbiAgdXNlcm5hbWU6IHByb2Nlc3MuZW52LkdJTklTX1VTRVJOQU1FID8/ICcnLFxuICBwYXNzd29yZDogcHJvY2Vzcy5lbnYuR0lOSVNfUEFTU1dPUkQgPz8gJycsXG4gIC8vIGlmIGRlYnVnID09PSB0cnVlIHByaW50cyBhbGwgdGhlIHJlcXVlc3RzIGFuZCByZXNwb25zZXMgaW50byBjb25zb2xlXG4gIC8vIHdhcm5pbmcgLSB0aGVzZSBsb2dzIFdJTEwgaW5jbHVkZSBjcmVkZW50aWFscyFcbiAgZGVidWc6IGZhbHNlLFxufSlcbiJdLCJuYW1lcyI6WyJHaW5pcyIsImdpbmlzIiwidXJscyIsInVkZSIsInByb2Nlc3MiLCJlbnYiLCJHSU5JU19IT1NUX1VERSIsInVzZXJuYW1lIiwiR0lOSVNfVVNFUk5BTUUiLCJwYXNzd29yZCIsIkdJTklTX1BBU1NXT1JEIiwiZGVidWciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./src/services/ginis/ginis.ts\n");

/***/ }),

/***/ "(api)/./src/services/ginis/mocks.ts":
/*!*************************************!*\
  !*** ./src/services/ginis/mocks.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getALotOfMockedDocs: () => (/* binding */ getALotOfMockedDocs),\n/* harmony export */   mockedParsedCategories: () => (/* binding */ mockedParsedCategories),\n/* harmony export */   mockedParsedDocumentDetail: () => (/* binding */ mockedParsedDocumentDetail),\n/* harmony export */   mockedParsedDocuments: () => (/* binding */ mockedParsedDocuments)\n/* harmony export */ });\n// Because the GINIS backend is accessible only from internal Bratislava network,\n// if you need placeholder data, you can use the following mocks:\nconst mockedParsedDocuments = [\n    {\n        id: \"mock-1\",\n        title: \"Mocked document title 1\",\n        publishedFrom: \"2022-01-01\",\n        description: \"hello world\",\n        numberOfFiles: 1,\n        categoryName: \"Mocked category name 1\"\n    },\n    {\n        id: \"mock-2\",\n        title: \"Mocked document title 1\",\n        publishedFrom: \"2022-01-01\",\n        description: \"hello world\",\n        numberOfFiles: 2,\n        categoryName: \"Mocked category name 2\"\n    },\n    {\n        id: \"mock-3\",\n        title: \"Mocked document title 1\",\n        publishedFrom: \"2022-01-01\",\n        description: \"hello world\",\n        numberOfFiles: 1,\n        categoryName: \"Mocked category name 3\"\n    }\n];\nconst getALotOfMockedDocs = async ()=>{\n    return [\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments,\n        ...mockedParsedDocuments\n    ];\n};\nconst mockedParsedDocumentDetail = {\n    id: \"mocked-document-1\",\n    title: \"Mocked document title 1\",\n    publishedFrom: \"2022-01-01\",\n    description: \"hello world\",\n    categoryName: \"Mocked category name 1\",\n    files: [\n        {\n            id: \"mocked-file-1\",\n            title: \"Mocked file 1\",\n            generatedUrl: \"#\",\n            size: \"1 kB\"\n        },\n        {\n            id: \"mocked-file-2\",\n            title: \"Mocked file 2\",\n            generatedUrl: \"#\",\n            size: \"2 MB\"\n        }\n    ]\n};\nconst mockedParsedCategories = [\n    {\n        id: \"Bytov\\xe1 n\\xe1hrada - z\\xe1kon č. 260 z r. 2011\",\n        title: \"Bytov\\xe1 n\\xe1hrada\",\n        numberOfPostedDocuments: 0,\n        numberOfArchivedDocuments: 0\n    },\n    {\n        id: \"In\\xe9 \\xfaradn\\xe9 oznamy\",\n        title: \"In\\xe9 \\xfaradn\\xe9 oznamy\",\n        numberOfPostedDocuments: 5,\n        numberOfArchivedDocuments: 275\n    },\n    {\n        id: \"Komun\\xe1lne voľby\",\n        title: \"Komun\\xe1lne voľby\",\n        numberOfPostedDocuments: 1,\n        numberOfArchivedDocuments: 41\n    },\n    {\n        id: \"Mestsk\\xe1 rada a zastupiteľstvo\",\n        title: \"Mestsk\\xe1 rada a zastupiteľstvo\",\n        numberOfPostedDocuments: 0,\n        numberOfArchivedDocuments: 169\n    },\n    {\n        id: \"N\\xe1vrhy VzN a dodatkov k Štat\\xfatu na pripomienkovani\",\n        title: \"N\\xe1vrhy VzN, dodatkov k Štat\\xfatu na pripomienkovanie\",\n        numberOfPostedDocuments: 0,\n        numberOfArchivedDocuments: 58\n    },\n    {\n        id: \"Ozn\\xe1menia o dražbe\",\n        title: \"Dražba\",\n        numberOfPostedDocuments: 8,\n        numberOfArchivedDocuments: 479\n    },\n    {\n        id: \"Posudzovanie vplyvov na životn\\xe9 prostredie\",\n        title: \"Posudzovanie vplyvov na životn\\xe9 prostredie\",\n        numberOfPostedDocuments: 8,\n        numberOfArchivedDocuments: 875\n    },\n    {\n        id: \"Rozpočet hl.m. SR Bratislavy\",\n        title: \"Rozpočet\",\n        numberOfPostedDocuments: 0,\n        numberOfArchivedDocuments: 12\n    },\n    {\n        id: \"Smogov\\xfd varovn\\xfd sign\\xe1l\",\n        title: \"Smogov\\xfd varovn\\xfd sign\\xe1l\",\n        numberOfPostedDocuments: 0,\n        numberOfArchivedDocuments: 6\n    },\n    {\n        id: \"Soci\\xe1lne služby\",\n        title: \"Soci\\xe1lne služby\",\n        numberOfPostedDocuments: 3,\n        numberOfArchivedDocuments: 10\n    },\n    {\n        id: \"Stavebn\\xe9 povolenia\",\n        title: \"Stavebn\\xe9 povolenia\",\n        numberOfPostedDocuments: 0,\n        numberOfArchivedDocuments: 202\n    },\n    {\n        id: \"Udržba komunik\\xe1ci\\xed\",\n        title: \"\\xdadržba komunik\\xe1ci\\xed\",\n        numberOfPostedDocuments: 122,\n        numberOfArchivedDocuments: 2410\n    },\n    {\n        id: \"Verejn\\xe9 obstar\\xe1vanie\",\n        title: \"Verejn\\xe9 obstar\\xe1vanie\",\n        numberOfPostedDocuments: 26,\n        numberOfArchivedDocuments: 39\n    },\n    {\n        id: \"Verejn\\xe9 vyhl\\xe1šky\",\n        title: \"Verejn\\xe9 vyhl\\xe1šky\",\n        numberOfPostedDocuments: 8,\n        numberOfArchivedDocuments: 1621\n    },\n    {\n        id: \"Verejn\\xfd n\\xe1vrh\",\n        title: \"Verejn\\xfd n\\xe1vrh\",\n        numberOfPostedDocuments: 0,\n        numberOfArchivedDocuments: 3\n    },\n    {\n        id: \"Všeobecne z\\xe1v\\xe4zn\\xe9 nariadenia\",\n        title: \"Všeobecne z\\xe1v\\xe4zn\\xe9 nariadenia a Štat\\xfat hl.m.\",\n        numberOfPostedDocuments: 1,\n        numberOfArchivedDocuments: 139\n    },\n    {\n        id: \"V\\xfdberov\\xe9 konania a zamestnanie\",\n        title: \"V\\xfdberov\\xe9 konania a zamestnanie\",\n        numberOfPostedDocuments: 15,\n        numberOfArchivedDocuments: 1552\n    },\n    {\n        id: \"V\\xfdsledky vybavenia pet\\xedci\\xed\",\n        title: \"Pet\\xedcie a sťažnosti\",\n        numberOfPostedDocuments: 17,\n        numberOfArchivedDocuments: 82\n    },\n    {\n        id: \"Z\\xe1mery, verejn\\xe9 s\\xfaťaže a dražby\",\n        title: \"Z\\xe1mery, verejn\\xe9 s\\xfaťaže a dražby\",\n        numberOfPostedDocuments: 0,\n        numberOfArchivedDocuments: 373\n    },\n    {\n        id: \"Životn\\xe9 prostredie, mestsk\\xe1 zeleň\",\n        title: \"Životn\\xe9 prostredie, mestsk\\xe1 zeleň\",\n        numberOfPostedDocuments: 1,\n        numberOfArchivedDocuments: 255\n    },\n    {\n        id: \"\\xdazemn\\xe9 pl\\xe1novanie, \\xdazemn\\xfd rozvoj mesta\",\n        title: \"\\xdazemn\\xe9 pl\\xe1novanie a rozvoj\",\n        numberOfPostedDocuments: 0,\n        numberOfArchivedDocuments: 42\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/services/ginis/mocks.ts\n");

/***/ }),

/***/ "(api)/./src/services/ginis/server/getOfficialBoardParsedList.ts":
/*!*****************************************************************!*\
  !*** ./src/services/ginis/server/getOfficialBoardParsedList.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getOfficialBoardParsedList: () => (/* binding */ getOfficialBoardParsedList)\n/* harmony export */ });\n/* harmony import */ var _src_services_ginis_ginis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/src/services/ginis/ginis */ \"(api)/./src/services/ginis/ginis.ts\");\n/* harmony import */ var _src_utils_isDefined__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/src/utils/isDefined */ \"(api)/./src/utils/isDefined.ts\");\n\n\nconst getOfficialBoardParsedList = async (options)=>{\n    const { searchQuery, publicationState, categoryId } = options;\n    let documents = [];\n    // Nazev - Max. délka: 254\n    const searchQueryTrimmed = searchQuery?.trim().slice(0, 254) ?? \"\";\n    // Stav?: 'vyveseno' | 'sejmuto'\n    const publicationStateSanitized = publicationState === \"vyveseno\" || publicationState === \"sejmuto\" ? publicationState : undefined;\n    try {\n        /**\n     * The `bodyObj` uses same keys as the requests in Ginis docs (dash-case, only first capital, Czech language)\n     * It will throw an GinisError if the request fails - if the cause is axios error, it's available in error.axiosError\n     * https://robot.gordic.cz/xrg/Default.html?c=OpenMethodDetail&moduleName=UDE&version=390&methodName=seznam-dokumentu&type=request\n     */ const response = await _src_services_ginis_ginis__WEBPACK_IMPORTED_MODULE_0__.ginis.ude.seznamDokumentu({\n            Stav: publicationStateSanitized,\n            \"Id-kategorie\": categoryId,\n            Nazev: searchQueryTrimmed\n        });\n        documents = response[\"Seznam-dokumentu\"];\n    } catch (error) {\n        // TODO handle error\n        // eslint-disable-next-line no-console\n        console.log(error);\n    }\n    const parsedDocuments = documents.map((document)=>{\n        return {\n            id: document[\"Id-zaznamu\"],\n            title: document.Nazev,\n            publishedFrom: document[\"Vyveseno-dne\"],\n            publishedTo: document[\"Sejmuto-dne\"],\n            description: document.Popis ?? \"\",\n            numberOfFiles: parseInt(document[\"Pocet-souboru\"] ?? 0, 10),\n            categoryName: document.Kategorie\n        };\n    }).filter(_src_utils_isDefined__WEBPACK_IMPORTED_MODULE_1__.isDefined);\n    return parsedDocuments;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/services/ginis/server/getOfficialBoardParsedList.ts\n");

/***/ }),

/***/ "(api)/./src/services/ginis/utils/shouldMockGinis.ts":
/*!*****************************************************!*\
  !*** ./src/services/ginis/utils/shouldMockGinis.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   shouldMockGinis: () => (/* binding */ shouldMockGinis)\n/* harmony export */ });\n// GINIS is accessible only from internal network\n// if developing from internal network, change here\nconst shouldMockGinis = ()=>{\n    return  true || 0;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvc2VydmljZXMvZ2luaXMvdXRpbHMvc2hvdWxkTW9ja0dpbmlzLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpREFBaUQ7QUFDakQsbURBQW1EO0FBQzVDLE1BQU1BLGtCQUFrQjtJQUM3QixPQUNFQyxLQUN5QixJQUN6QkEsQ0FBbUI7QUFFdkIsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2JyYXRpc2xhdmEtbmV4dC8uL3NyYy9zZXJ2aWNlcy9naW5pcy91dGlscy9zaG91bGRNb2NrR2luaXMudHM/YmQ3MCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBHSU5JUyBpcyBhY2Nlc3NpYmxlIG9ubHkgZnJvbSBpbnRlcm5hbCBuZXR3b3JrXG4vLyBpZiBkZXZlbG9waW5nIGZyb20gaW50ZXJuYWwgbmV0d29yaywgY2hhbmdlIGhlcmVcbmV4cG9ydCBjb25zdCBzaG91bGRNb2NrR2luaXMgPSAoKSA9PiB7XG4gIHJldHVybiAoXG4gICAgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcgfHxcbiAgICBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Rlc3QnIHx8XG4gICAgcHJvY2Vzcy5lbnYuQ0kgPT09ICd0cnVlJ1xuICApXG59XG4iXSwibmFtZXMiOlsic2hvdWxkTW9ja0dpbmlzIiwicHJvY2VzcyIsImVudiIsIkNJIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./src/services/ginis/utils/shouldMockGinis.ts\n");

/***/ }),

/***/ "(api)/./src/utils/isDefined.ts":
/*!********************************!*\
  !*** ./src/utils/isDefined.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasAttributes: () => (/* binding */ hasAttributes),\n/* harmony export */   isDefined: () => (/* binding */ isDefined),\n/* harmony export */   withAttributes: () => (/* binding */ withAttributes)\n/* harmony export */ });\nfunction isDefined(value) {\n    return value !== undefined && value !== null;\n}\nfunction hasAttributes(value) {\n    return isDefined(value) && \"attributes\" in value && isDefined(value.attributes);\n}\nfunction withAttributes(value) {\n    if (isDefined(value)) {\n        if (hasAttributes(value)) {\n            return value;\n        }\n        return null;\n    }\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvdXRpbHMvaXNEZWZpbmVkLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFPLFNBQVNBLFVBQWFDLEtBQTJCO0lBQ3RELE9BQU9BLFVBQVVDLGFBQWFELFVBQVU7QUFDMUM7QUFZTyxTQUFTRSxjQUNkRixLQUEyQjtJQUUzQixPQUFPRCxVQUFVQyxVQUFVLGdCQUFnQkEsU0FBU0QsVUFBVUMsTUFBTUcsVUFBVTtBQUNoRjtBQUVPLFNBQVNDLGVBQ2RKLEtBQTJCO0lBRTNCLElBQUlELFVBQVVDLFFBQVE7UUFDcEIsSUFBSUUsY0FBY0YsUUFBUTtZQUN4QixPQUFPQTtRQUNUO1FBRUEsT0FBTztJQUNUO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2JyYXRpc2xhdmEtbmV4dC8uL3NyYy91dGlscy9pc0RlZmluZWQudHM/ZDc2NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gaXNEZWZpbmVkPFQ+KHZhbHVlOiBUIHwgdW5kZWZpbmVkIHwgbnVsbCk6IHZhbHVlIGlzIFQge1xuICByZXR1cm4gdmFsdWUgIT09IHVuZGVmaW5lZCAmJiB2YWx1ZSAhPT0gbnVsbFxufVxuXG5leHBvcnQgdHlwZSBPYmpXaXRoQXR0cmlidXRlcyA9IHtcbiAgYXR0cmlidXRlcz86IFJlY29yZDxzdHJpbmcsIHVua25vd24+IHwgbnVsbCB8IHVuZGVmaW5lZFxufVxuXG5leHBvcnQgdHlwZSBXaXRoUmVxdWlyZWQ8VHlwZSwgS2V5IGV4dGVuZHMga2V5b2YgVHlwZT4gPSBUeXBlICYge1xuICBbUHJvcGVydHkgaW4gS2V5XS0/OiBOb25OdWxsYWJsZTxUeXBlW1Byb3BlcnR5XT5cbn1cblxuZXhwb3J0IHR5cGUgV2l0aEF0dHJpYnV0ZXM8VHlwZSBleHRlbmRzIE9ialdpdGhBdHRyaWJ1dGVzPiA9IFdpdGhSZXF1aXJlZDxUeXBlLCAnYXR0cmlidXRlcyc+XG5cbmV4cG9ydCBmdW5jdGlvbiBoYXNBdHRyaWJ1dGVzPFQgZXh0ZW5kcyBPYmpXaXRoQXR0cmlidXRlcz4oXG4gIHZhbHVlOiBUIHwgbnVsbCB8IHVuZGVmaW5lZCxcbik6IHZhbHVlIGlzIFdpdGhBdHRyaWJ1dGVzPFQ+IHtcbiAgcmV0dXJuIGlzRGVmaW5lZCh2YWx1ZSkgJiYgJ2F0dHJpYnV0ZXMnIGluIHZhbHVlICYmIGlzRGVmaW5lZCh2YWx1ZS5hdHRyaWJ1dGVzKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gd2l0aEF0dHJpYnV0ZXM8VCBleHRlbmRzIE9ialdpdGhBdHRyaWJ1dGVzPihcbiAgdmFsdWU6IFQgfCBudWxsIHwgdW5kZWZpbmVkLFxuKTogV2l0aEF0dHJpYnV0ZXM8VD4gfCBudWxsIHwgdW5kZWZpbmVkIHtcbiAgaWYgKGlzRGVmaW5lZCh2YWx1ZSkpIHtcbiAgICBpZiAoaGFzQXR0cmlidXRlcyh2YWx1ZSkpIHtcbiAgICAgIHJldHVybiB2YWx1ZVxuICAgIH1cblxuICAgIHJldHVybiBudWxsXG4gIH1cblxuICByZXR1cm4gdmFsdWVcbn1cbiJdLCJuYW1lcyI6WyJpc0RlZmluZWQiLCJ2YWx1ZSIsInVuZGVmaW5lZCIsImhhc0F0dHJpYnV0ZXMiLCJhdHRyaWJ1dGVzIiwid2l0aEF0dHJpYnV0ZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./src/utils/isDefined.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fginis%2Fofficial-board-list&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cginis%5Cofficial-board-list.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();