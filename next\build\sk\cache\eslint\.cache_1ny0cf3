[{"C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\images\\index.ts": "1", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\CardBase.tsx": "2", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\CardContent.tsx": "3", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\CategoryCard.tsx": "4", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\ContactCtaCard.tsx": "5", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\EventCard.tsx": "6", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\FileRowCard.tsx": "7", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\FileRowCardWrapper.tsx": "8", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\HomepageHorizontalCard.tsx": "9", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\InBaCard.tsx": "10", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\InbaReleaseHorizontalCard.tsx": "11", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\MayorAndCouncilCard.tsx": "12", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\TopServicesItem.tsx": "13", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Accordion\\Accordion.tsx": "14", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Alert_Deprecated\\Alert_Deprecated.tsx": "15", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Alert_Deprecated\\CloseIcon.tsx": "16", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Alert_Deprecated\\ErrorIcon.tsx": "17", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Alert_Deprecated\\InfoIcon.tsx": "18", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Alert_Deprecated\\SuccessIcon.tsx": "19", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Alert_Deprecated\\WarningIcon.tsx": "20", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\AliasInfoMessage\\AliasInfoMessage.tsx": "21", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Banner\\Banner.tsx": "22", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Brand\\Brand.tsx": "23", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Breadcrumbs\\Breadcrumbs.tsx": "24", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Breadcrumbs\\DesktopBreadcrumbs.tsx": "25", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Breadcrumbs\\MobileBreadcrumbs.tsx": "26", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Button\\Button.tsx": "27", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Carousel\\Carousel.tsx": "28", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Carousel\\ResponsiveCarousel.tsx": "29", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Chip\\Chip.tsx": "30", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\ColumnedText\\ColumnedText.tsx": "31", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\ComparisonCard\\ComparisonCard.tsx": "32", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Contacts\\Contacts.tsx": "33", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\CopyToClipboardButton\\CopyToClipboardButton.tsx": "34", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Divider\\Divider.tsx": "35", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Divider\\HorizontalDivider.tsx": "36", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\FaqsGroup\\FaqsGroup.tsx": "37", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\FileList\\FileList.tsx": "38", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Footer\\DesktopFooter.tsx": "39", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Footer\\Footer.tsx": "40", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Footer\\FooterShared.tsx": "41", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Footer\\MobileFooter.tsx": "42", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Gallery\\Gallery.tsx": "43", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Gallery\\GalleryModal.tsx": "44", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Gallery\\GallerySlider.tsx": "45", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Gallery\\ImageLightBox.tsx": "46", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\HomepageSearch\\HomePageSearch.tsx": "47", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\HomepageSearch\\HomePageSearchField.tsx": "48", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\HomepageSearch\\HomePageSearchResults.tsx": "49", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\IconTitleDescItem\\IconTitleDescItem.tsx": "50", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Iframe\\Iframe.tsx": "51", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Image\\ImagePlaceholder.tsx": "52", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Image\\StrapiImage.tsx": "53", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\InbaArticlesFilter\\InbaArticlesFilter.tsx": "54", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Institution_Deprecated\\Institution_Deprecated.tsx": "55", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Links\\Links.tsx": "56", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\LoadingSpinner\\LoadingSpinner.tsx": "57", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\MLink\\MLink.tsx": "58", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\ModalDialog\\Dialog.tsx": "59", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\ModalDialog\\Modal.tsx": "60", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NarrowText\\NarrowText.tsx": "61", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\AlertBanner.tsx": "62", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\MobileNavBar.tsx": "63", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavBar.tsx": "64", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavBarHeader\\NavBarHeader.tsx": "65", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\getParsedMenus.ts": "66", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\MobileNavMenu.tsx": "67", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\MobileNavMenuContent.tsx": "68", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\MobileNavMenuItem.tsx": "69", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\MobileNavMenuTrigger.tsx": "70", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavBarHorizontalDivider.tsx": "71", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavMenu.tsx": "72", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavMenuContent.tsx": "73", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavMenuContentCell.tsx": "74", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\navMenuContext.tsx": "75", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavMenuItem.tsx": "76", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavMenuLink.tsx": "77", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavMenuSection.tsx": "78", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavMenuTrigger.tsx": "79", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\navMenuTypes.ts": "80", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NumericalList_Deprecated\\CompleteDashedLineSvg_Deprecated.tsx": "81", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NumericalList_Deprecated\\DashedLine_Deprecated.tsx": "82", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NumericalList_Deprecated\\NumericalListItem_Deprecated.tsx": "83", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NumericalList_Deprecated\\NumericalList_Deprecated.tsx": "84", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\OrganizationalStructure_Deprecated\\OrganizationalStructureAccordionCards_Deprecated.tsx": "85", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\OrganizationalStructure_Deprecated\\OrganizationalStructureAccordionCard_Deprecated.tsx": "86", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\OrganizationalStructure_Deprecated\\OrganizationalStructureAccordion_Deprecated.tsx": "87", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\OrganizationalStructure_Deprecated\\OrganizationalStructureTopLevelAccordion_Deprecated.tsx": "88", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\OrganizationalStructure_Deprecated\\OrganizationalStructure_Deprecated.tsx": "89", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\PageHeader\\PageHeader.tsx": "90", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Pagination\\Pagination.tsx": "91", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Pagination\\usePagination.ts": "92", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Pictogram\\getPictogram.ts": "93", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Pictogram\\Pictogram.tsx": "94", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Radio\\Radio.tsx": "95", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\RadioGroup\\RadioGroup.tsx": "96", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Regulations\\Regulations.tsx": "97", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\ScrollToTopButton\\ScrollToTopButton.tsx": "98", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\SectionContainer\\SectionContainer.tsx": "99", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\SelectField\\SelectField.tsx": "100", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\SkipToContentButton\\SkipToContentButton.tsx": "101", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Spinner\\Spinner.tsx": "102", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\SubpageList_Deprecated\\SubpageList_Deprecated.tsx": "103", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Tag\\Tag.tsx": "104", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Timeline\\Timeline.tsx": "105", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\TopServices\\TopServices.tsx": "106", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Videos_Deprecated\\Videos_Deprecated.tsx": "107", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Waves\\waves\\WaveBottomLarge.tsx": "108", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Waves\\waves\\WaveBottomSmall.tsx": "109", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Waves\\waves\\WaveTopLarge.tsx": "110", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Waves\\waves\\WaveTopSmall.tsx": "111", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Waves\\Waves.tsx": "112", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Waves\\wavesTypes.ts": "113", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\formatting\\AnimateHeight.tsx": "114", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\formatting\\FormatEventDateRange.tsx": "115", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\formatting\\Markdown\\Markdown.tsx": "116", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\layouts\\PageHeaderSections.tsx": "117", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\layouts\\PageLayout.tsx": "118", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\layouts\\Sections.tsx": "119", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\ArticlePageContent.tsx": "120", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\GeneralPageContent.tsx": "121", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\HomepageContent.tsx": "122", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\InbaArticlePageContent.tsx": "123", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\InbaReleasePageContent.tsx": "124", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\OfficialBoardDocumentPageContent.tsx": "125", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\RegulationPageContent.tsx": "126", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\providers\\BAI18nProvider.tsx": "127", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\providers\\BAQueryClientProvider.tsx": "128", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\providers\\GeneralContextProvider.tsx": "129", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\providers\\HomepageContextProvider.tsx": "130", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\providers\\LocalizationsProvider.tsx": "131", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\AccordionSection.tsx": "132", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ArticlesListSection\\ArticlesAllSection.tsx": "133", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ArticlesListSection\\ArticlesByCategory.tsx": "134", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ArticlesListSection\\ArticlesSection.tsx": "135", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ArticlesListSection\\InbaArticlesList.tsx": "136", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\BannerSection.tsx": "137", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\CalculatorSection_Deprecated\\CalculatorSection_Deprecated.tsx": "138", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\CalculatorSection_Deprecated\\Input_Deprecated.tsx": "139", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\CalculatorSection_Deprecated\\MinimumCalculator_Deprecated.tsx": "140", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ColumnedTextSection.tsx": "141", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ComparisonSection.tsx": "142", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ContactsSection.tsx": "143", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\DividerSection.tsx": "144", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\FaqCategoriesSection.tsx": "145", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\FaqsSection.tsx": "146", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\FileListSection.tsx": "147", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\GallerySection.tsx": "148", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\HighlightsHomepageSection.tsx": "149", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\HomepageTabs\\HomepageTabs.tsx": "150", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\HomepageTabs\\TabPanelDisclosure.tsx": "151", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\HomepageTabs\\TabPanelLatestNews.tsx": "152", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\HomepageTabs\\TabPanelOfficialBoard.tsx": "153", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\HomepageTabs\\TabPanelRoadClosures.tsx": "154", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\InbaHomepageSection.tsx": "155", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\MayorAndCouncilHomepageSection.tsx": "156", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\NewsAndInfoHomepageSection.tsx": "157", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\TopServicesHomepageSection.tsx": "158", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\WelcomeHomepageSection.tsx": "159", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\IconTitleDescSection.tsx": "160", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\IframeSection.tsx": "161", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\InbaReleasesSection.tsx": "162", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\LinksSection.tsx": "163", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\NarrowTextSection.tsx": "164", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\NumericalListSection.tsx": "165", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\OfficialBoardSection\\OfficialBoardAdditionalFilters.tsx": "166", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\OfficialBoardSection\\officialBoardCategories.mock.ts": "167", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\OfficialBoardSection\\OfficialBoardSection.tsx": "168", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\OrganizationalStructureSection.tsx": "169", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ProsAndConsSection.tsx": "170", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\RegulationsListSection.tsx": "171", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\RegulationsSection.tsx": "172", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\RelatedArticlesSection.tsx": "173", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SearchSection\\GlobalSearchSectionContent.tsx": "174", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SearchSection\\LoadingOverlay.tsx": "175", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SearchSection\\SearchBar.tsx": "176", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SearchSection\\SearchResultCard.tsx": "177", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SearchSection\\SearchResults.tsx": "178", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SearchSection\\SearchResultsHeader.tsx": "179", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SearchSection\\useQueryBySearchOption.ts": "180", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SubpageListPageHeaderSection_Deprecated.tsx": "181", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\TextWithImageSection.tsx": "182", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\TimelineSection.tsx": "183", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\VideosSection.tsx": "184", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\AccordionShowcase.tsx": "185", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\AlertShowCase.tsx": "186", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\BannerShowCase.tsx": "187", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\ButtonShowCase.tsx": "188", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\CategoryCardShowcase.tsx": "189", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\ContactsShowcase.tsx": "190", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\EventCardShowcase.tsx": "191", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\HomepageHorizontalCardShowcase.tsx": "192", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\SpinnerShowCase.tsx": "193", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\TagShowCase.tsx": "194", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\Stack.tsx": "195", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\StyleGuideWrapper.tsx": "196", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\Wrapper.tsx": "197", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\404.tsx": "198", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\feed.ts": "199", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\ginis\\official-board-categories.ts": "200", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\ginis\\official-board-file\\[fileId]\\[fileName].ts": "201", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\ginis\\official-board-list.ts": "202", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\healthcheck.ts": "203", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\ms-graph\\search-in-structure.ts": "204", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\ms-graph\\structure.ts": "205", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\revalidate.ts": "206", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\robots.ts": "207", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\inba\\archiv\\[slug].tsx": "208", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\inba\\clanky\\[slug].tsx": "209", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\index.tsx": "210", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\spravy\\[slug].tsx": "211", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\styleguide.tsx": "212", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\uradna-tabula\\[slug].tsx": "213", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\vyhladavanie.tsx": "214", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\[...slug].tsx": "215", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\_app.tsx": "216", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\fetchers\\homepageContextFetcher.ts": "217", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\consts.ts": "218", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\fetchers\\officialBoardCategoriesFetcher.ts": "219", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\fetchers\\officialBoardListFetcher.ts": "220", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\ginis.ts": "221", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\mocks.ts": "222", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\server\\getOfficialBoardFileBase64Encoded.ts": "223", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\server\\getOfficialBoardParsedCategories.ts": "224", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\server\\getOfficialBoardParsedDocument.ts": "225", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\server\\getOfficialBoardParsedList.ts": "226", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\types.ts": "227", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\utils\\generateUrlForOfficialBoardFile.ts": "228", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\utils\\shouldMockGinis.ts": "229", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\fetchers\\articlesFetcher.ts": "230", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\fetchers\\homepageSearchFetcher.ts": "231", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\fetchers\\inbaArticlesFetcher.ts": "232", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\fetchers\\pagesFetcher.ts": "233", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\fetchers\\regulationsFetcher.ts": "234", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\fetchers\\relatedArticlesFetcher.ts": "235", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\meiliClient.ts": "236", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\types.ts": "237", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\utils.ts": "238", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\fetchers\\msGraphSearch.fetcher.ts": "239", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\fetchers\\msGraphStructure.fetcher.ts": "240", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\server\\constants.ts": "241", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\server\\getGroupMembers.ts": "242", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\server\\getMsalToken.ts": "243", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\server\\msalClient.ts": "244", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\server\\searchInOrgStructure.ts": "245", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\types.ts": "246", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\tootoot\\constants.ts": "247", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\base64.ts": "248", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\cn.ts": "249", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\colors.tsx": "250", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\consts.ts": "251", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\dev\\strapiHelper.ts": "252", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\formatDate.ts": "253", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\formatFileExtension.ts": "254", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\formatFileSize.ts": "255", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\generateImageSizes.ts": "256", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\getIconByPageColor.ts": "257", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\isDefined.ts": "258", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\isExternalLink.ts": "259", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\onEnterOrSpaceKeyDown.ts": "260", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\pageUtils_Deprecated.ts": "261", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\prefetchPageSections.ts": "262", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\useGetDownloadAriaLabel.tsx": "263", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\useLocale.ts": "264", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\useRegulationCategoryTranslationMap.ts": "265", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\useRoutePreservedState.ts": "266", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\useTitle.ts": "267", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\useTranslation.ts": "268", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\utils.ts": "269", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\ArticleCard.tsx": "270", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\ArticlesFilter\\ArticlesFilter.tsx": "271", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\ArticleCardShowcase.tsx": "272", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\icons\\index.ts": "273", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\icons-contacts\\index.ts": "274", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\icons-social-media\\index.ts": "275", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\material-icons\\index.ts": "276", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\pictograms-regulation-categories\\index.ts": "277", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\pictograms-search-results\\index.ts": "278", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\pictograms-top-services\\index.ts": "279", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\RegulationRowCard.tsx": "280", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\transformArticleProps.ts": "281", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\transformInbaArticleProps.ts": "282", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\ColumnsSectionItem\\ColumnsSectionItem.tsx": "283", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\PageHeader\\DocumentPageHeader.tsx": "284", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Regulations\\RegulationDetailMessage.tsx": "285", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Slider\\Slider.tsx": "286", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Slider\\useSlider.ts": "287", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\DocumentPageContent.tsx": "288", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ColumnsSection.tsx": "289", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\DocumentsSection.tsx": "290", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\PartnersSection.tsx": "291", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ShareButtons_Deprecated.tsx": "292", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\TextWithImageOverlappedSection.tsx": "293", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\TootootEventsSection.tsx": "294", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\ColumnsShowcase.tsx": "295", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\MarkdownShowcase.tsx": "296", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\TokensShowcase.tsx": "297", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\dokumenty\\[slug].tsx": "298", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\_error.jsx": "299", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\tootoot\\tootootEvents.fetcher.tsx": "300", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\getLinkProps.ts": "301", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\getRegulationMetadata.ts": "302", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\screens.ts": "303", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\useTailwindBreakpointValue.ts": "304", "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\DocumentRowCard.tsx": "305"}, {"size": 124, "mtime": 1748959240050, "results": "306", "hashOfConfig": "307"}, {"size": 678, "mtime": 1747953625746, "results": "308", "hashOfConfig": "307"}, {"size": 329, "mtime": 1747953625760, "results": "309", "hashOfConfig": "307"}, {"size": 954, "mtime": 1748462780095, "results": "310", "hashOfConfig": "307"}, {"size": 4824, "mtime": 1750348999078, "results": "311", "hashOfConfig": "307"}, {"size": 2438, "mtime": 1750749120698, "results": "312", "hashOfConfig": "307"}, {"size": 2744, "mtime": 1748959240120, "results": "313", "hashOfConfig": "307"}, {"size": 1722, "mtime": 1747953625869, "results": "314", "hashOfConfig": "307"}, {"size": 1728, "mtime": 1748462780102, "results": "315", "hashOfConfig": "307"}, {"size": 2119, "mtime": 1748462780102, "results": "316", "hashOfConfig": "307"}, {"size": 2235, "mtime": 1748959240121, "results": "317", "hashOfConfig": "307"}, {"size": 1213, "mtime": 1748462780103, "results": "318", "hashOfConfig": "307"}, {"size": 1318, "mtime": 1748462780103, "results": "319", "hashOfConfig": "307"}, {"size": 2293, "mtime": 1748959240122, "results": "320", "hashOfConfig": "307"}, {"size": 4355, "mtime": 1748959240122, "results": "321", "hashOfConfig": "307"}, {"size": 1056, "mtime": 1747953626060, "results": "322", "hashOfConfig": "307"}, {"size": 1181, "mtime": 1747953626073, "results": "323", "hashOfConfig": "307"}, {"size": 1174, "mtime": 1747953626087, "results": "324", "hashOfConfig": "307"}, {"size": 1094, "mtime": 1747953626100, "results": "325", "hashOfConfig": "307"}, {"size": 979, "mtime": 1747953626112, "results": "326", "hashOfConfig": "307"}, {"size": 1085, "mtime": 1748217005089, "results": "327", "hashOfConfig": "307"}, {"size": 3386, "mtime": 1747953626181, "results": "328", "hashOfConfig": "307"}, {"size": 1077, "mtime": 1748959240122, "results": "329", "hashOfConfig": "307"}, {"size": 628, "mtime": 1747953626212, "results": "330", "hashOfConfig": "307"}, {"size": 1536, "mtime": 1748959240124, "results": "331", "hashOfConfig": "307"}, {"size": 2962, "mtime": 1748959240124, "results": "332", "hashOfConfig": "307"}, {"size": 9321, "mtime": 1750749120699, "results": "333", "hashOfConfig": "307"}, {"size": 5678, "mtime": 1748959240125, "results": "334", "hashOfConfig": "307"}, {"size": 960, "mtime": 1747953626324, "results": "335", "hashOfConfig": "307"}, {"size": 1018, "mtime": 1747953626778, "results": "336", "hashOfConfig": "307"}, {"size": 1395, "mtime": 1748959240126, "results": "337", "hashOfConfig": "307"}, {"size": 1612, "mtime": 1748959240127, "results": "338", "hashOfConfig": "307"}, {"size": 2216, "mtime": 1750348999081, "results": "339", "hashOfConfig": "307"}, {"size": 609, "mtime": 1748959240129, "results": "340", "hashOfConfig": "307"}, {"size": 5846, "mtime": 1747953626413, "results": "341", "hashOfConfig": "307"}, {"size": 570, "mtime": 1747953626514, "results": "342", "hashOfConfig": "307"}, {"size": 867, "mtime": 1747953626527, "results": "343", "hashOfConfig": "307"}, {"size": 1515, "mtime": 1747953626542, "results": "344", "hashOfConfig": "307"}, {"size": 2784, "mtime": 1748959240129, "results": "345", "hashOfConfig": "307"}, {"size": 374, "mtime": 1747953626572, "results": "346", "hashOfConfig": "307"}, {"size": 3028, "mtime": 1750762876637, "results": "347", "hashOfConfig": "307"}, {"size": 2618, "mtime": 1748959240130, "results": "348", "hashOfConfig": "307"}, {"size": 4977, "mtime": 1747953626628, "results": "349", "hashOfConfig": "307"}, {"size": 3151, "mtime": 1748959240131, "results": "350", "hashOfConfig": "307"}, {"size": 5128, "mtime": 1747953626684, "results": "351", "hashOfConfig": "307"}, {"size": 3421, "mtime": 1748959240131, "results": "352", "hashOfConfig": "307"}, {"size": 2754, "mtime": 1747953626723, "results": "353", "hashOfConfig": "307"}, {"size": 2165, "mtime": 1748959240132, "results": "354", "hashOfConfig": "307"}, {"size": 2982, "mtime": 1748959240132, "results": "355", "hashOfConfig": "307"}, {"size": 1504, "mtime": 1747953626796, "results": "356", "hashOfConfig": "307"}, {"size": 2171, "mtime": 1747953626815, "results": "357", "hashOfConfig": "307"}, {"size": 293, "mtime": 1748959240132, "results": "358", "hashOfConfig": "307"}, {"size": 1073, "mtime": 1747953626840, "results": "359", "hashOfConfig": "307"}, {"size": 2251, "mtime": 1750348999083, "results": "360", "hashOfConfig": "307"}, {"size": 1404, "mtime": 1748462784613, "results": "361", "hashOfConfig": "307"}, {"size": 1391, "mtime": 1748959240133, "results": "362", "hashOfConfig": "307"}, {"size": 769, "mtime": 1747953626910, "results": "363", "hashOfConfig": "307"}, {"size": 1855, "mtime": 1747953626926, "results": "364", "hashOfConfig": "307"}, {"size": 2521, "mtime": 1748959240133, "results": "365", "hashOfConfig": "307"}, {"size": 1849, "mtime": 1747953626963, "results": "366", "hashOfConfig": "307"}, {"size": 942, "mtime": 1747953626979, "results": "367", "hashOfConfig": "307"}, {"size": 2261, "mtime": 1748959240134, "results": "368", "hashOfConfig": "307"}, {"size": 3380, "mtime": 1748959240134, "results": "369", "hashOfConfig": "307"}, {"size": 806, "mtime": 1747953627035, "results": "370", "hashOfConfig": "307"}, {"size": 3126, "mtime": 1748959240135, "results": "371", "hashOfConfig": "307"}, {"size": 1712, "mtime": 1748959240137, "results": "372", "hashOfConfig": "307"}, {"size": 4078, "mtime": 1747953627108, "results": "373", "hashOfConfig": "307"}, {"size": 2301, "mtime": 1748959240135, "results": "374", "hashOfConfig": "307"}, {"size": 779, "mtime": 1747953627144, "results": "375", "hashOfConfig": "307"}, {"size": 1417, "mtime": 1748959240136, "results": "376", "hashOfConfig": "307"}, {"size": 338, "mtime": 1747953627177, "results": "377", "hashOfConfig": "307"}, {"size": 1723, "mtime": 1747953627201, "results": "378", "hashOfConfig": "307"}, {"size": 3612, "mtime": 1747953627229, "results": "379", "hashOfConfig": "307"}, {"size": 434, "mtime": 1747953627250, "results": "380", "hashOfConfig": "307"}, {"size": 878, "mtime": 1747953627269, "results": "381", "hashOfConfig": "307"}, {"size": 789, "mtime": 1747953627287, "results": "382", "hashOfConfig": "307"}, {"size": 1487, "mtime": 1747953627304, "results": "383", "hashOfConfig": "307"}, {"size": 1267, "mtime": 1747953627322, "results": "384", "hashOfConfig": "307"}, {"size": 1293, "mtime": 1748959240136, "results": "385", "hashOfConfig": "307"}, {"size": 514, "mtime": 1747953627358, "results": "386", "hashOfConfig": "307"}, {"size": 778, "mtime": 1747953627374, "results": "387", "hashOfConfig": "307"}, {"size": 637, "mtime": 1747953627394, "results": "388", "hashOfConfig": "307"}, {"size": 2236, "mtime": 1747953627434, "results": "389", "hashOfConfig": "307"}, {"size": 1686, "mtime": 1747953627412, "results": "390", "hashOfConfig": "307"}, {"size": 1044, "mtime": 1747953627521, "results": "391", "hashOfConfig": "307"}, {"size": 1372, "mtime": 1749073941945, "results": "392", "hashOfConfig": "307"}, {"size": 2479, "mtime": 1748959240137, "results": "393", "hashOfConfig": "307"}, {"size": 2148, "mtime": 1748959240138, "results": "394", "hashOfConfig": "307"}, {"size": 1388, "mtime": 1748959240138, "results": "395", "hashOfConfig": "307"}, {"size": 4735, "mtime": 1747953627570, "results": "396", "hashOfConfig": "307"}, {"size": 3689, "mtime": 1748959240139, "results": "397", "hashOfConfig": "307"}, {"size": 4169, "mtime": 1747953627627, "results": "398", "hashOfConfig": "307"}, {"size": 14893, "mtime": 1748959240139, "results": "399", "hashOfConfig": "307"}, {"size": 590, "mtime": 1747953627677, "results": "400", "hashOfConfig": "307"}, {"size": 1204, "mtime": 1748959240140, "results": "401", "hashOfConfig": "307"}, {"size": 832, "mtime": 1747953627720, "results": "402", "hashOfConfig": "307"}, {"size": 3446, "mtime": 1747953627770, "results": "403", "hashOfConfig": "307"}, {"size": 1307, "mtime": 1748959240141, "results": "404", "hashOfConfig": "307"}, {"size": 418, "mtime": 1748959240141, "results": "405", "hashOfConfig": "307"}, {"size": 3552, "mtime": 1748959240142, "results": "406", "hashOfConfig": "307"}, {"size": 913, "mtime": 1747953627855, "results": "407", "hashOfConfig": "307"}, {"size": 1011, "mtime": 1747953627874, "results": "408", "hashOfConfig": "307"}, {"size": 1777, "mtime": 1747953627893, "results": "409", "hashOfConfig": "307"}, {"size": 1706, "mtime": 1748959240142, "results": "410", "hashOfConfig": "307"}, {"size": 1121, "mtime": 1748462784618, "results": "411", "hashOfConfig": "307"}, {"size": 1302, "mtime": 1747953627959, "results": "412", "hashOfConfig": "307"}, {"size": 3206, "mtime": 1748959240142, "results": "413", "hashOfConfig": "307"}, {"size": 3228, "mtime": 1747953628012, "results": "414", "hashOfConfig": "307"}, {"size": 3572, "mtime": 1747953628026, "results": "415", "hashOfConfig": "307"}, {"size": 3436, "mtime": 1747953628038, "results": "416", "hashOfConfig": "307"}, {"size": 3434, "mtime": 1747953628051, "results": "417", "hashOfConfig": "307"}, {"size": 1085, "mtime": 1747953627998, "results": "418", "hashOfConfig": "307"}, {"size": 74, "mtime": 1747953628062, "results": "419", "hashOfConfig": "307"}, {"size": 1544, "mtime": 1747953628076, "results": "420", "hashOfConfig": "307"}, {"size": 3789, "mtime": 1747953628097, "results": "421", "hashOfConfig": "307"}, {"size": 7106, "mtime": 1748217005091, "results": "422", "hashOfConfig": "307"}, {"size": 986, "mtime": 1747953628134, "results": "423", "hashOfConfig": "307"}, {"size": 747, "mtime": 1747953628144, "results": "424", "hashOfConfig": "307"}, {"size": 6652, "mtime": 1750762881191, "results": "425", "hashOfConfig": "307"}, {"size": 3021, "mtime": 1748959240144, "results": "426", "hashOfConfig": "307"}, {"size": 1904, "mtime": 1747953628193, "results": "427", "hashOfConfig": "307"}, {"size": 1392, "mtime": 1747953628205, "results": "428", "hashOfConfig": "307"}, {"size": 3632, "mtime": 1748959240145, "results": "429", "hashOfConfig": "307"}, {"size": 3751, "mtime": 1748959240145, "results": "430", "hashOfConfig": "307"}, {"size": 3840, "mtime": 1747953628260, "results": "431", "hashOfConfig": "307"}, {"size": 9812, "mtime": 1748959240145, "results": "432", "hashOfConfig": "307"}, {"size": 811, "mtime": 1747953628302, "results": "433", "hashOfConfig": "307"}, {"size": 513, "mtime": 1747953628314, "results": "434", "hashOfConfig": "307"}, {"size": 696, "mtime": 1747953628327, "results": "435", "hashOfConfig": "307"}, {"size": 797, "mtime": 1747953628338, "results": "436", "hashOfConfig": "307"}, {"size": 1677, "mtime": 1747953628353, "results": "437", "hashOfConfig": "307"}, {"size": 4475, "mtime": 1748216171041, "results": "438", "hashOfConfig": "307"}, {"size": 2880, "mtime": 1747953628400, "results": "439", "hashOfConfig": "307"}, {"size": 2834, "mtime": 1747953628421, "results": "440", "hashOfConfig": "307"}, {"size": 557, "mtime": 1747953628436, "results": "441", "hashOfConfig": "307"}, {"size": 2754, "mtime": 1747953628466, "results": "442", "hashOfConfig": "307"}, {"size": 671, "mtime": 1747953628480, "results": "443", "hashOfConfig": "307"}, {"size": 613, "mtime": 1748959240146, "results": "444", "hashOfConfig": "307"}, {"size": 598, "mtime": 1748959240146, "results": "445", "hashOfConfig": "307"}, {"size": 5810, "mtime": 1748959240147, "results": "446", "hashOfConfig": "307"}, {"size": 451, "mtime": 1748462784622, "results": "447", "hashOfConfig": "307"}, {"size": 1977, "mtime": 1748959240148, "results": "448", "hashOfConfig": "307"}, {"size": 365, "mtime": 1747953628601, "results": "449", "hashOfConfig": "307"}, {"size": 379, "mtime": 1747953628615, "results": "450", "hashOfConfig": "307"}, {"size": 1450, "mtime": 1747953628629, "results": "451", "hashOfConfig": "307"}, {"size": 894, "mtime": 1747953628643, "results": "452", "hashOfConfig": "307"}, {"size": 521, "mtime": 1747953628653, "results": "453", "hashOfConfig": "307"}, {"size": 765, "mtime": 1747953628669, "results": "454", "hashOfConfig": "307"}, {"size": 2334, "mtime": 1747953628690, "results": "455", "hashOfConfig": "307"}, {"size": 2226, "mtime": 1747998443050, "results": "456", "hashOfConfig": "307"}, {"size": 914, "mtime": 1748959240154, "results": "457", "hashOfConfig": "307"}, {"size": 3620, "mtime": 1747953628749, "results": "458", "hashOfConfig": "307"}, {"size": 3798, "mtime": 1748959240154, "results": "459", "hashOfConfig": "307"}, {"size": 1164, "mtime": 1747953628790, "results": "460", "hashOfConfig": "307"}, {"size": 1188, "mtime": 1747953628805, "results": "461", "hashOfConfig": "307"}, {"size": 1825, "mtime": 1747953628823, "results": "462", "hashOfConfig": "307"}, {"size": 906, "mtime": 1747953628840, "results": "463", "hashOfConfig": "307"}, {"size": 957, "mtime": 1747953628855, "results": "464", "hashOfConfig": "307"}, {"size": 1790, "mtime": 1747953628871, "results": "465", "hashOfConfig": "307"}, {"size": 1568, "mtime": 1747953628886, "results": "466", "hashOfConfig": "307"}, {"size": 338, "mtime": 1747953628898, "results": "467", "hashOfConfig": "307"}, {"size": 2574, "mtime": 1747953628920, "results": "468", "hashOfConfig": "307"}, {"size": 369, "mtime": 1747953628931, "results": "469", "hashOfConfig": "307"}, {"size": 624, "mtime": 1747953628947, "results": "470", "hashOfConfig": "307"}, {"size": 786, "mtime": 1747953628960, "results": "471", "hashOfConfig": "307"}, {"size": 4000, "mtime": 1748959240148, "results": "472", "hashOfConfig": "307"}, {"size": 3142, "mtime": 1747953628999, "results": "473", "hashOfConfig": "307"}, {"size": 300, "mtime": 1747953629012, "results": "474", "hashOfConfig": "307"}, {"size": 541, "mtime": 1747953629029, "results": "475", "hashOfConfig": "307"}, {"size": 1797, "mtime": 1748959240148, "results": "476", "hashOfConfig": "307"}, {"size": 302, "mtime": 1747953629058, "results": "477", "hashOfConfig": "307"}, {"size": 381, "mtime": 1747953629069, "results": "478", "hashOfConfig": "307"}, {"size": 1796, "mtime": 1747953629084, "results": "479", "hashOfConfig": "307"}, {"size": 11031, "mtime": 1750348999086, "results": "480", "hashOfConfig": "307"}, {"size": 1084, "mtime": 1747953629132, "results": "481", "hashOfConfig": "307"}, {"size": 2689, "mtime": 1748959240149, "results": "482", "hashOfConfig": "307"}, {"size": 5924, "mtime": 1748959240150, "results": "483", "hashOfConfig": "307"}, {"size": 4632, "mtime": 1750348999087, "results": "484", "hashOfConfig": "307"}, {"size": 1008, "mtime": 1748959240151, "results": "485", "hashOfConfig": "307"}, {"size": 9397, "mtime": 1750348999088, "results": "486", "hashOfConfig": "307"}, {"size": 494, "mtime": 1747953629278, "results": "487", "hashOfConfig": "307"}, {"size": 2811, "mtime": 1748462784624, "results": "488", "hashOfConfig": "307"}, {"size": 461, "mtime": 1747953629347, "results": "489", "hashOfConfig": "307"}, {"size": 553, "mtime": 1747953629379, "results": "490", "hashOfConfig": "307"}, {"size": 570, "mtime": 1748959240155, "results": "491", "hashOfConfig": "307"}, {"size": 7929, "mtime": 1747953629424, "results": "492", "hashOfConfig": "307"}, {"size": 1058, "mtime": 1747953629452, "results": "493", "hashOfConfig": "307"}, {"size": 9080, "mtime": 1748959240156, "results": "494", "hashOfConfig": "307"}, {"size": 1246, "mtime": 1747953629509, "results": "495", "hashOfConfig": "307"}, {"size": 1877, "mtime": 1750348999089, "results": "496", "hashOfConfig": "307"}, {"size": 1562, "mtime": 1747953629546, "results": "497", "hashOfConfig": "307"}, {"size": 1138, "mtime": 1747953629560, "results": "498", "hashOfConfig": "307"}, {"size": 433, "mtime": 1747953629582, "results": "499", "hashOfConfig": "307"}, {"size": 1116, "mtime": 1748959240156, "results": "500", "hashOfConfig": "307"}, {"size": 555, "mtime": 1748959240155, "results": "501", "hashOfConfig": "307"}, {"size": 2627, "mtime": 1748959240155, "results": "502", "hashOfConfig": "307"}, {"size": 964, "mtime": 1747953629658, "results": "503", "hashOfConfig": "307"}, {"size": 1716, "mtime": 1747953629724, "results": "504", "hashOfConfig": "307"}, {"size": 2260, "mtime": 1747953629745, "results": "505", "hashOfConfig": "307"}, {"size": 885, "mtime": 1747953629759, "results": "506", "hashOfConfig": "307"}, {"size": 2387, "mtime": 1750348999091, "results": "507", "hashOfConfig": "307"}, {"size": 2232, "mtime": 1747953629800, "results": "508", "hashOfConfig": "307"}, {"size": 195, "mtime": 1747953629810, "results": "509", "hashOfConfig": "307"}, {"size": 1093, "mtime": 1747953629829, "results": "510", "hashOfConfig": "307"}, {"size": 849, "mtime": 1747953629844, "results": "511", "hashOfConfig": "307"}, {"size": 1541, "mtime": 1748959240158, "results": "512", "hashOfConfig": "307"}, {"size": 622, "mtime": 1747953629872, "results": "513", "hashOfConfig": "307"}, {"size": 2679, "mtime": 1747953629893, "results": "514", "hashOfConfig": "307"}, {"size": 2910, "mtime": 1747953629913, "results": "515", "hashOfConfig": "307"}, {"size": 2777, "mtime": 1747953629925, "results": "516", "hashOfConfig": "307"}, {"size": 3090, "mtime": 1750762881194, "results": "517", "hashOfConfig": "307"}, {"size": 2603, "mtime": 1748462784626, "results": "518", "hashOfConfig": "307"}, {"size": 2680, "mtime": 1750762881195, "results": "519", "hashOfConfig": "307"}, {"size": 2267, "mtime": 1748867704873, "results": "520", "hashOfConfig": "307"}, {"size": 5231, "mtime": 1747953629712, "results": "521", "hashOfConfig": "307"}, {"size": 3074, "mtime": 1747953629682, "results": "522", "hashOfConfig": "307"}, {"size": 720, "mtime": 1747953630022, "results": "523", "hashOfConfig": "307"}, {"size": 76, "mtime": 1747953630032, "results": "524", "hashOfConfig": "307"}, {"size": 348, "mtime": 1747953630045, "results": "525", "hashOfConfig": "307"}, {"size": 1170, "mtime": 1747953630057, "results": "526", "hashOfConfig": "307"}, {"size": 513, "mtime": 1747953630065, "results": "527", "hashOfConfig": "307"}, {"size": 5602, "mtime": 1748959240159, "results": "528", "hashOfConfig": "307"}, {"size": 781, "mtime": 1747953630107, "results": "529", "hashOfConfig": "307"}, {"size": 1336, "mtime": 1747953630122, "results": "530", "hashOfConfig": "307"}, {"size": 1770, "mtime": 1747953630136, "results": "531", "hashOfConfig": "307"}, {"size": 2038, "mtime": 1747953630152, "results": "532", "hashOfConfig": "307"}, {"size": 1013, "mtime": 1747953630164, "results": "533", "hashOfConfig": "307"}, {"size": 459, "mtime": 1747953630176, "results": "534", "hashOfConfig": "307"}, {"size": 273, "mtime": 1747953630186, "results": "535", "hashOfConfig": "307"}, {"size": 3036, "mtime": 1747953631312, "results": "536", "hashOfConfig": "307"}, {"size": 2557, "mtime": 1747953631328, "results": "537", "hashOfConfig": "307"}, {"size": 2365, "mtime": 1747953631347, "results": "538", "hashOfConfig": "307"}, {"size": 1313, "mtime": 1747953631369, "results": "539", "hashOfConfig": "307"}, {"size": 927, "mtime": 1747953631387, "results": "540", "hashOfConfig": "307"}, {"size": 950, "mtime": 1747953631399, "results": "541", "hashOfConfig": "307"}, {"size": 432, "mtime": 1747953631411, "results": "542", "hashOfConfig": "307"}, {"size": 1627, "mtime": 1747953631424, "results": "543", "hashOfConfig": "307"}, {"size": 591, "mtime": 1747953631435, "results": "544", "hashOfConfig": "307"}, {"size": 383, "mtime": 1747953631452, "results": "545", "hashOfConfig": "307"}, {"size": 311, "mtime": 1747953631467, "results": "546", "hashOfConfig": "307"}, {"size": 516, "mtime": 1747953631483, "results": "547", "hashOfConfig": "307"}, {"size": 4132, "mtime": 1747953631510, "results": "548", "hashOfConfig": "307"}, {"size": 356, "mtime": 1747953631522, "results": "549", "hashOfConfig": "307"}, {"size": 405, "mtime": 1747953631536, "results": "550", "hashOfConfig": "307"}, {"size": 1936, "mtime": 1747953631551, "results": "551", "hashOfConfig": "307"}, {"size": 2209, "mtime": 1747953631564, "results": "552", "hashOfConfig": "307"}, {"size": 2175, "mtime": 1747953631579, "results": "553", "hashOfConfig": "307"}, {"size": 205, "mtime": 1747953631677, "results": "554", "hashOfConfig": "307"}, {"size": 1051, "mtime": 1747953631690, "results": "555", "hashOfConfig": "307"}, {"size": 2623, "mtime": 1748001439000, "results": "556", "hashOfConfig": "307"}, {"size": 178, "mtime": 1747953631717, "results": "557", "hashOfConfig": "307"}, {"size": 7589, "mtime": 1748959240162, "results": "558", "hashOfConfig": "307"}, {"size": 263, "mtime": 1747953631778, "results": "559", "hashOfConfig": "307"}, {"size": 216, "mtime": 1747953631790, "results": "560", "hashOfConfig": "307"}, {"size": 286, "mtime": 1747953631802, "results": "561", "hashOfConfig": "307"}, {"size": 1008, "mtime": 1748462784630, "results": "562", "hashOfConfig": "307"}, {"size": 916, "mtime": 1748959240162, "results": "563", "hashOfConfig": "307"}, {"size": 896, "mtime": 1747953631867, "results": "564", "hashOfConfig": "307"}, {"size": 151, "mtime": 1747953631876, "results": "565", "hashOfConfig": "307"}, {"size": 253, "mtime": 1747953631884, "results": "566", "hashOfConfig": "307"}, {"size": 1592, "mtime": 1747953631898, "results": "567", "hashOfConfig": "307"}, {"size": 2404, "mtime": 1747953631914, "results": "568", "hashOfConfig": "307"}, {"size": 1000, "mtime": 1747953631927, "results": "569", "hashOfConfig": "307"}, {"size": 142, "mtime": 1747953631936, "results": "570", "hashOfConfig": "307"}, {"size": 766, "mtime": 1747953631947, "results": "571", "hashOfConfig": "307"}, {"size": 1373, "mtime": 1747953672806, "results": "572", "hashOfConfig": "307"}, {"size": 265, "mtime": 1747953631975, "results": "573", "hashOfConfig": "307"}, {"size": 153, "mtime": 1747953631986, "results": "574", "hashOfConfig": "307"}, {"size": 408, "mtime": 1747953631995, "results": "575", "hashOfConfig": "307"}, {"size": 2314, "mtime": 1748462780095, "results": "576", "hashOfConfig": "307"}, {"size": 6333, "mtime": 1750348999080, "results": "577", "hashOfConfig": "307"}, {"size": 1578, "mtime": 1747953629437, "results": "578", "hashOfConfig": "307"}, {"size": 3588, "mtime": 1748959240033, "results": "579", "hashOfConfig": "307"}, {"size": 398, "mtime": 1748959240022, "results": "580", "hashOfConfig": "307"}, {"size": 232, "mtime": 1748959240025, "results": "581", "hashOfConfig": "307"}, {"size": 367, "mtime": 1750749120697, "results": "582", "hashOfConfig": "307"}, {"size": 823, "mtime": 1748959240106, "results": "583", "hashOfConfig": "307"}, {"size": 1144, "mtime": 1748959240109, "results": "584", "hashOfConfig": "307"}, {"size": 887, "mtime": 1748959240114, "results": "585", "hashOfConfig": "307"}, {"size": 2955, "mtime": 1749073941945, "results": "586", "hashOfConfig": "307"}, {"size": 1113, "mtime": 1747953625982, "results": "587", "hashOfConfig": "307"}, {"size": 942, "mtime": 1747953625994, "results": "588", "hashOfConfig": "307"}, {"size": 2275, "mtime": 1748959240127, "results": "589", "hashOfConfig": "307"}, {"size": 3856, "mtime": 1750749120700, "results": "590", "hashOfConfig": "307"}, {"size": 4244, "mtime": 1748959240140, "results": "591", "hashOfConfig": "307"}, {"size": 3925, "mtime": 1748462784616, "results": "592", "hashOfConfig": "307"}, {"size": 3072, "mtime": 1748462784618, "results": "593", "hashOfConfig": "307"}, {"size": 4538, "mtime": 1750749120701, "results": "594", "hashOfConfig": "307"}, {"size": 4177, "mtime": 1748867699180, "results": "595", "hashOfConfig": "307"}, {"size": 3881, "mtime": 1750749120701, "results": "596", "hashOfConfig": "307"}, {"size": 2686, "mtime": 1748522644810, "results": "597", "hashOfConfig": "307"}, {"size": 2670, "mtime": 1748959240151, "results": "598", "hashOfConfig": "307"}, {"size": 4711, "mtime": 1748462784624, "results": "599", "hashOfConfig": "307"}, {"size": 3032, "mtime": 1748959240153, "results": "600", "hashOfConfig": "307"}, {"size": 2621, "mtime": 1748959240156, "results": "601", "hashOfConfig": "307"}, {"size": 1496, "mtime": 1748462784626, "results": "602", "hashOfConfig": "307"}, {"size": 4277, "mtime": 1747953629616, "results": "603", "hashOfConfig": "307"}, {"size": 2865, "mtime": 1750749066655, "results": "604", "hashOfConfig": "307"}, {"size": 432, "mtime": 1748959240156, "results": "605", "hashOfConfig": "307"}, {"size": 4115, "mtime": 1747953631596, "results": "606", "hashOfConfig": "307"}, {"size": 1540, "mtime": 1747953631844, "results": "607", "hashOfConfig": "307"}, {"size": 1314, "mtime": 1748959240164, "results": "608", "hashOfConfig": "307"}, {"size": 539, "mtime": 1748462784630, "results": "609", "hashOfConfig": "307"}, {"size": 380, "mtime": 1748462784630, "results": "610", "hashOfConfig": "307"}, {"size": 3741, "mtime": 1750762876634, "results": "611", "hashOfConfig": "307"}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qbjio9", {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1227", "messages": "1228", "suppressedMessages": "1229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1230", "messages": "1231", "suppressedMessages": "1232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1233", "messages": "1234", "suppressedMessages": "1235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1236", "messages": "1237", "suppressedMessages": "1238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1239", "messages": "1240", "suppressedMessages": "1241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1242", "messages": "1243", "suppressedMessages": "1244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1245", "messages": "1246", "suppressedMessages": "1247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1248", "messages": "1249", "suppressedMessages": "1250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1251", "messages": "1252", "suppressedMessages": "1253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1254", "messages": "1255", "suppressedMessages": "1256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1257", "messages": "1258", "suppressedMessages": "1259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1260", "messages": "1261", "suppressedMessages": "1262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1263", "messages": "1264", "suppressedMessages": "1265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1266", "messages": "1267", "suppressedMessages": "1268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1269", "messages": "1270", "suppressedMessages": "1271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1272", "messages": "1273", "suppressedMessages": "1274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1275", "messages": "1276", "suppressedMessages": "1277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1278", "messages": "1279", "suppressedMessages": "1280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1281", "messages": "1282", "suppressedMessages": "1283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1284", "messages": "1285", "suppressedMessages": "1286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1287", "messages": "1288", "suppressedMessages": "1289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1290", "messages": "1291", "suppressedMessages": "1292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1293", "messages": "1294", "suppressedMessages": "1295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1296", "messages": "1297", "suppressedMessages": "1298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1299", "messages": "1300", "suppressedMessages": "1301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1302", "messages": "1303", "suppressedMessages": "1304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1305", "messages": "1306", "suppressedMessages": "1307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1308", "messages": "1309", "suppressedMessages": "1310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1311", "messages": "1312", "suppressedMessages": "1313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1314", "messages": "1315", "suppressedMessages": "1316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1317", "messages": "1318", "suppressedMessages": "1319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1320", "messages": "1321", "suppressedMessages": "1322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1323", "messages": "1324", "suppressedMessages": "1325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1326", "messages": "1327", "suppressedMessages": "1328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1329", "messages": "1330", "suppressedMessages": "1331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1332", "messages": "1333", "suppressedMessages": "1334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1335", "messages": "1336", "suppressedMessages": "1337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1338", "messages": "1339", "suppressedMessages": "1340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1341", "messages": "1342", "suppressedMessages": "1343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1344", "messages": "1345", "suppressedMessages": "1346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1347", "messages": "1348", "suppressedMessages": "1349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1350", "messages": "1351", "suppressedMessages": "1352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1353", "messages": "1354", "suppressedMessages": "1355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1356", "messages": "1357", "suppressedMessages": "1358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1359", "messages": "1360", "suppressedMessages": "1361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1362", "messages": "1363", "suppressedMessages": "1364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1365", "messages": "1366", "suppressedMessages": "1367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1368", "messages": "1369", "suppressedMessages": "1370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1371", "messages": "1372", "suppressedMessages": "1373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1374", "messages": "1375", "suppressedMessages": "1376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1377", "messages": "1378", "suppressedMessages": "1379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1380", "messages": "1381", "suppressedMessages": "1382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1383", "messages": "1384", "suppressedMessages": "1385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1386", "messages": "1387", "suppressedMessages": "1388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1389", "messages": "1390", "suppressedMessages": "1391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1392", "messages": "1393", "suppressedMessages": "1394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1395", "messages": "1396", "suppressedMessages": "1397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1398", "messages": "1399", "suppressedMessages": "1400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1401", "messages": "1402", "suppressedMessages": "1403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1404", "messages": "1405", "suppressedMessages": "1406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1407", "messages": "1408", "suppressedMessages": "1409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1410", "messages": "1411", "suppressedMessages": "1412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1413", "messages": "1414", "suppressedMessages": "1415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1416", "messages": "1417", "suppressedMessages": "1418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1419", "messages": "1420", "suppressedMessages": "1421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1422", "messages": "1423", "suppressedMessages": "1424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1425", "messages": "1426", "suppressedMessages": "1427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1428", "messages": "1429", "suppressedMessages": "1430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1431", "messages": "1432", "suppressedMessages": "1433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1434", "messages": "1435", "suppressedMessages": "1436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1437", "messages": "1438", "suppressedMessages": "1439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1440", "messages": "1441", "suppressedMessages": "1442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1443", "messages": "1444", "suppressedMessages": "1445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1446", "messages": "1447", "suppressedMessages": "1448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1449", "messages": "1450", "suppressedMessages": "1451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1452", "messages": "1453", "suppressedMessages": "1454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1455", "messages": "1456", "suppressedMessages": "1457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1458", "messages": "1459", "suppressedMessages": "1460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1461", "messages": "1462", "suppressedMessages": "1463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1464", "messages": "1465", "suppressedMessages": "1466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1467", "messages": "1468", "suppressedMessages": "1469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1470", "messages": "1471", "suppressedMessages": "1472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1473", "messages": "1474", "suppressedMessages": "1475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1476", "messages": "1477", "suppressedMessages": "1478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1479", "messages": "1480", "suppressedMessages": "1481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1482", "messages": "1483", "suppressedMessages": "1484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1485", "messages": "1486", "suppressedMessages": "1487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1488", "messages": "1489", "suppressedMessages": "1490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1491", "messages": "1492", "suppressedMessages": "1493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1494", "messages": "1495", "suppressedMessages": "1496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1497", "messages": "1498", "suppressedMessages": "1499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1500", "messages": "1501", "suppressedMessages": "1502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1503", "messages": "1504", "suppressedMessages": "1505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1506", "messages": "1507", "suppressedMessages": "1508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1509", "messages": "1510", "suppressedMessages": "1511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1512", "messages": "1513", "suppressedMessages": "1514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1515", "messages": "1516", "suppressedMessages": "1517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1518", "messages": "1519", "suppressedMessages": "1520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1521", "messages": "1522", "suppressedMessages": "1523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1524", "messages": "1525", "suppressedMessages": "1526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\images\\index.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\CardBase.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\CardContent.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\CategoryCard.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\ContactCtaCard.tsx", [], ["1527", "1528"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\EventCard.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\FileRowCard.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\FileRowCardWrapper.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\HomepageHorizontalCard.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\InBaCard.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\InbaReleaseHorizontalCard.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\MayorAndCouncilCard.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\TopServicesItem.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Accordion\\Accordion.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Alert_Deprecated\\Alert_Deprecated.tsx", [], ["1529"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Alert_Deprecated\\CloseIcon.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Alert_Deprecated\\ErrorIcon.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Alert_Deprecated\\InfoIcon.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Alert_Deprecated\\SuccessIcon.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Alert_Deprecated\\WarningIcon.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\AliasInfoMessage\\AliasInfoMessage.tsx", [], ["1530"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Banner\\Banner.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Brand\\Brand.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Breadcrumbs\\Breadcrumbs.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Breadcrumbs\\DesktopBreadcrumbs.tsx", [], ["1531"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Breadcrumbs\\MobileBreadcrumbs.tsx", [], ["1532"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Button\\Button.tsx", [], ["1533"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Carousel\\Carousel.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Carousel\\ResponsiveCarousel.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Chip\\Chip.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\ColumnedText\\ColumnedText.tsx", [], ["1534", "1535"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\ComparisonCard\\ComparisonCard.tsx", [], ["1536"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Contacts\\Contacts.tsx", [], ["1537", "1538"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\CopyToClipboardButton\\CopyToClipboardButton.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Divider\\Divider.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Divider\\HorizontalDivider.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\FaqsGroup\\FaqsGroup.tsx", [], ["1539"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\FileList\\FileList.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Footer\\DesktopFooter.tsx", [], ["1540"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Footer\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Footer\\FooterShared.tsx", [], ["1541"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Footer\\MobileFooter.tsx", [], ["1542"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Gallery\\Gallery.tsx", [], ["1543", "1544", "1545", "1546"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Gallery\\GalleryModal.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Gallery\\GallerySlider.tsx", [], ["1547", "1548", "1549"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Gallery\\ImageLightBox.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\HomepageSearch\\HomePageSearch.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\HomepageSearch\\HomePageSearchField.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\HomepageSearch\\HomePageSearchResults.tsx", [], ["1550", "1551", "1552"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\IconTitleDescItem\\IconTitleDescItem.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Iframe\\Iframe.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Image\\ImagePlaceholder.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Image\\StrapiImage.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\InbaArticlesFilter\\InbaArticlesFilter.tsx", [], ["1553"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Institution_Deprecated\\Institution_Deprecated.tsx", [], ["1554"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Links\\Links.tsx", [], ["1555"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\LoadingSpinner\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\MLink\\MLink.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\ModalDialog\\Dialog.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\ModalDialog\\Modal.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NarrowText\\NarrowText.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\AlertBanner.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\MobileNavBar.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavBar.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavBarHeader\\NavBarHeader.tsx", [], ["1556"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\getParsedMenus.ts", [], ["1557", "1558"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\MobileNavMenu.tsx", [], ["1559", "1560"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\MobileNavMenuContent.tsx", [], ["1561"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\MobileNavMenuItem.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\MobileNavMenuTrigger.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavBarHorizontalDivider.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavMenu.tsx", [], ["1562"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavMenuContent.tsx", [], ["1563", "1564", "1565", "1566", "1567", "1568"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavMenuContentCell.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\navMenuContext.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavMenuItem.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavMenuLink.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavMenuSection.tsx", [], ["1569"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\NavMenuTrigger.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NavBar\\NavMenu\\navMenuTypes.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NumericalList_Deprecated\\CompleteDashedLineSvg_Deprecated.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NumericalList_Deprecated\\DashedLine_Deprecated.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NumericalList_Deprecated\\NumericalListItem_Deprecated.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\NumericalList_Deprecated\\NumericalList_Deprecated.tsx", [], ["1570"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\OrganizationalStructure_Deprecated\\OrganizationalStructureAccordionCards_Deprecated.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\OrganizationalStructure_Deprecated\\OrganizationalStructureAccordionCard_Deprecated.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\OrganizationalStructure_Deprecated\\OrganizationalStructureAccordion_Deprecated.tsx", [], ["1571"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\OrganizationalStructure_Deprecated\\OrganizationalStructureTopLevelAccordion_Deprecated.tsx", [], ["1572", "1573"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\OrganizationalStructure_Deprecated\\OrganizationalStructure_Deprecated.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\PageHeader\\PageHeader.tsx", [], ["1574"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Pagination\\Pagination.tsx", [], ["1575", "1576"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Pagination\\usePagination.ts", [], ["1577"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Pictogram\\getPictogram.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Pictogram\\Pictogram.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Radio\\Radio.tsx", [], ["1578", "1579"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\RadioGroup\\RadioGroup.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Regulations\\Regulations.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\ScrollToTopButton\\ScrollToTopButton.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\SectionContainer\\SectionContainer.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\SelectField\\SelectField.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\SkipToContentButton\\SkipToContentButton.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Spinner\\Spinner.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\SubpageList_Deprecated\\SubpageList_Deprecated.tsx", [], ["1580"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Tag\\Tag.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Timeline\\Timeline.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\TopServices\\TopServices.tsx", [], ["1581", "1582", "1583"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Videos_Deprecated\\Videos_Deprecated.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Waves\\waves\\WaveBottomLarge.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Waves\\waves\\WaveBottomSmall.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Waves\\waves\\WaveTopLarge.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Waves\\waves\\WaveTopSmall.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Waves\\Waves.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Waves\\wavesTypes.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\formatting\\AnimateHeight.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\formatting\\FormatEventDateRange.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\formatting\\Markdown\\Markdown.tsx", [], ["1584"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\layouts\\PageHeaderSections.tsx", [], ["1585", "1586"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\layouts\\PageLayout.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\layouts\\Sections.tsx", [], ["1587", "1588", "1589"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\ArticlePageContent.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\GeneralPageContent.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\HomepageContent.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\InbaArticlePageContent.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\InbaReleasePageContent.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\OfficialBoardDocumentPageContent.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\RegulationPageContent.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\providers\\BAI18nProvider.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\providers\\BAQueryClientProvider.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\providers\\GeneralContextProvider.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\providers\\HomepageContextProvider.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\providers\\LocalizationsProvider.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\AccordionSection.tsx", [], ["1590", "1591", "1592", "1593", "1594", "1595"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ArticlesListSection\\ArticlesAllSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ArticlesListSection\\ArticlesByCategory.tsx", [], ["1596"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ArticlesListSection\\ArticlesSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ArticlesListSection\\InbaArticlesList.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\BannerSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\CalculatorSection_Deprecated\\CalculatorSection_Deprecated.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\CalculatorSection_Deprecated\\Input_Deprecated.tsx", [], ["1597", "1598"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\CalculatorSection_Deprecated\\MinimumCalculator_Deprecated.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ColumnedTextSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ComparisonSection.tsx", [], ["1599"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ContactsSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\DividerSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\FaqCategoriesSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\FaqsSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\FileListSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\GallerySection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\HighlightsHomepageSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\HomepageTabs\\HomepageTabs.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\HomepageTabs\\TabPanelDisclosure.tsx", [], ["1600"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\HomepageTabs\\TabPanelLatestNews.tsx", [], ["1601"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\HomepageTabs\\TabPanelOfficialBoard.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\HomepageTabs\\TabPanelRoadClosures.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\InbaHomepageSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\MayorAndCouncilHomepageSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\NewsAndInfoHomepageSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\TopServicesHomepageSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\homepage-sections\\WelcomeHomepageSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\IconTitleDescSection.tsx", [], ["1602"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\IframeSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\InbaReleasesSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\LinksSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\NarrowTextSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\NumericalListSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\OfficialBoardSection\\OfficialBoardAdditionalFilters.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\OfficialBoardSection\\officialBoardCategories.mock.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\OfficialBoardSection\\OfficialBoardSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\OrganizationalStructureSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ProsAndConsSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\RegulationsListSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\RegulationsSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\RelatedArticlesSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SearchSection\\GlobalSearchSectionContent.tsx", [], ["1603"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SearchSection\\LoadingOverlay.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SearchSection\\SearchBar.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SearchSection\\SearchResultCard.tsx", ["1604", "1605", "1606", "1607", "1608", "1609", "1610"], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SearchSection\\SearchResults.tsx", [], ["1611"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SearchSection\\SearchResultsHeader.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SearchSection\\useQueryBySearchOption.ts", [], ["1612", "1613", "1614", "1615"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\SubpageListPageHeaderSection_Deprecated.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\TextWithImageSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\TimelineSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\VideosSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\AccordionShowcase.tsx", [], ["1616", "1617"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\AlertShowCase.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\BannerShowCase.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\ButtonShowCase.tsx", [], ["1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627", "1628", "1629", "1630", "1631", "1632", "1633", "1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646", "1647", "1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\CategoryCardShowcase.tsx", [], ["1667"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\ContactsShowcase.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\EventCardShowcase.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\HomepageHorizontalCardShowcase.tsx", [], ["1668"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\SpinnerShowCase.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\TagShowCase.tsx", [], ["1669", "1670", "1671"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\Stack.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\StyleGuideWrapper.tsx", [], ["1672", "1673", "1674", "1675"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\Wrapper.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\404.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\feed.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\ginis\\official-board-categories.ts", [], ["1676"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\ginis\\official-board-file\\[fileId]\\[fileName].ts", ["1677"], ["1678"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\ginis\\official-board-list.ts", [], ["1679"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\healthcheck.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\ms-graph\\search-in-structure.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\ms-graph\\structure.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\revalidate.ts", [], ["1680", "1681", "1682", "1683"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\api\\robots.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\inba\\archiv\\[slug].tsx", [], ["1684", "1685"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\inba\\clanky\\[slug].tsx", [], ["1686", "1687"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\index.tsx", [], ["1688"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\spravy\\[slug].tsx", [], ["1689", "1690"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\styleguide.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\uradna-tabula\\[slug].tsx", [], ["1691"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\vyhladavanie.tsx", [], ["1692"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\[...slug].tsx", [], ["1693", "1694"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\_app.tsx", [], ["1695", "1696"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\fetchers\\homepageContextFetcher.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\consts.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\fetchers\\officialBoardCategoriesFetcher.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\fetchers\\officialBoardListFetcher.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\ginis.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\mocks.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\server\\getOfficialBoardFileBase64Encoded.ts", [], ["1697"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\server\\getOfficialBoardParsedCategories.ts", [], ["1698"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\server\\getOfficialBoardParsedDocument.ts", [], ["1699"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\server\\getOfficialBoardParsedList.ts", [], ["1700"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\types.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\utils\\generateUrlForOfficialBoardFile.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ginis\\utils\\shouldMockGinis.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\fetchers\\articlesFetcher.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\fetchers\\homepageSearchFetcher.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\fetchers\\inbaArticlesFetcher.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\fetchers\\pagesFetcher.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\fetchers\\regulationsFetcher.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\fetchers\\relatedArticlesFetcher.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\meiliClient.ts", [], ["1701"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\types.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\meili\\utils.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\fetchers\\msGraphSearch.fetcher.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\fetchers\\msGraphStructure.fetcher.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\server\\constants.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\server\\getGroupMembers.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\server\\getMsalToken.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\server\\msalClient.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\server\\searchInOrgStructure.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\ms-graph\\types.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\tootoot\\constants.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\base64.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\cn.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\colors.tsx", [], ["1702", "1703"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\consts.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\dev\\strapiHelper.ts", [], ["1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727", "1728", "1729", "1730", "1731"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\formatDate.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\formatFileExtension.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\formatFileSize.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\generateImageSizes.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\getIconByPageColor.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\isDefined.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\isExternalLink.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\onEnterOrSpaceKeyDown.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\pageUtils_Deprecated.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\prefetchPageSections.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\useGetDownloadAriaLabel.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\useLocale.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\useRegulationCategoryTranslationMap.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\useRoutePreservedState.ts", [], ["1732", "1733", "1734"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\useTitle.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\useTranslation.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\utils.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\ArticleCard.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\ArticlesFilter\\ArticlesFilter.tsx", [], ["1735", "1736", "1737"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\ArticleCardShowcase.tsx", [], ["1738"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\icons\\index.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\icons-contacts\\index.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\icons-social-media\\index.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\material-icons\\index.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\pictograms-regulation-categories\\index.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\pictograms-search-results\\index.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\assets\\pictograms-top-services\\index.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\RegulationRowCard.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\transformArticleProps.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\transformInbaArticleProps.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\ColumnsSectionItem\\ColumnsSectionItem.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\PageHeader\\DocumentPageHeader.tsx", [], ["1739"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Regulations\\RegulationDetailMessage.tsx", [], ["1740", "1741", "1742", "1743", "1744", "1745"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Slider\\Slider.tsx", [], ["1746"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\common\\Slider\\useSlider.ts", [], ["1747", "1748"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\page-contents\\DocumentPageContent.tsx", [], ["1749"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ColumnsSection.tsx", [], ["1750", "1751", "1752"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\DocumentsSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\PartnersSection.tsx", [], ["1753"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\ShareButtons_Deprecated.tsx", [], ["1754", "1755"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\TextWithImageOverlappedSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\sections\\TootootEventsSection.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\ColumnsShowcase.tsx", [], ["1756"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\MarkdownShowcase.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\styleguide\\showcases\\TokensShowcase.tsx", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\dokumenty\\[slug].tsx", [], ["1757"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\pages\\_error.jsx", [], ["1758"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\services\\tootoot\\tootootEvents.fetcher.tsx", [], ["1759", "1760", "1761"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\getLinkProps.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\getRegulationMetadata.ts", [], ["1762"], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\screens.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\utils\\useTailwindBreakpointValue.ts", [], [], "C:\\Users\\<USER>\\Repos\\bratislava.sk-1\\next\\src\\components\\cards\\DocumentRowCard.tsx", [], ["1763"], {"ruleId": "1764", "severity": 2, "message": "1765", "line": 57, "column": 27, "nodeType": null, "messageId": "1766", "endLine": 57, "endColumn": 29, "suppressions": "1767"}, {"ruleId": "1768", "severity": 2, "message": "1769", "line": 81, "column": 23, "nodeType": "1770", "messageId": "1771", "endLine": 81, "endColumn": 25, "suggestions": "1772", "suppressions": "1773"}, {"ruleId": "1764", "severity": 2, "message": "1774", "line": 44, "column": 15, "nodeType": null, "messageId": "1766", "endLine": 44, "endColumn": 17, "suppressions": "1775"}, {"ruleId": "1776", "severity": 2, "message": "1777", "line": 31, "column": 56, "nodeType": "1778", "endLine": 32, "endColumn": 25, "suppressions": "1779"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 31, "column": 72, "nodeType": "1782", "messageId": "1783", "endLine": 31, "endColumn": 77, "suppressions": "1784"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 55, "column": 65, "nodeType": "1782", "messageId": "1783", "endLine": 55, "endColumn": 70, "suppressions": "1785"}, {"ruleId": "1764", "severity": 2, "message": "1786", "line": 86, "column": 5, "nodeType": null, "messageId": "1766", "endLine": 86, "endColumn": 7, "suppressions": "1787"}, {"ruleId": "1788", "severity": 2, "message": "1789", "line": 13, "column": 9, "nodeType": "1782", "endLine": 13, "endColumn": 18, "suppressions": "1790"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 33, "column": 18, "nodeType": "1782", "messageId": "1783", "endLine": 33, "endColumn": 23, "suppressions": "1791"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 38, "column": 24, "nodeType": "1782", "messageId": "1783", "endLine": 38, "endColumn": 29, "suppressions": "1792"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 41, "column": 18, "nodeType": "1782", "messageId": "1783", "endLine": 41, "endColumn": 23, "suppressions": "1793"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 48, "column": 18, "nodeType": "1782", "messageId": "1783", "endLine": 48, "endColumn": 23, "suppressions": "1794"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 21, "column": 29, "nodeType": "1782", "messageId": "1783", "endLine": 21, "endColumn": 34, "suppressions": "1795"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 48, "column": 64, "nodeType": "1782", "messageId": "1783", "endLine": 48, "endColumn": 69, "suppressions": "1796"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 73, "column": 66, "nodeType": "1782", "messageId": "1783", "endLine": 73, "endColumn": 71, "suppressions": "1797"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 44, "column": 28, "nodeType": "1782", "messageId": "1783", "endLine": 44, "endColumn": 33, "suppressions": "1798"}, {"ruleId": "1799", "severity": 2, "message": "1800", "line": 82, "column": 21, "nodeType": "1801", "endLine": 93, "endColumn": 22, "suppressions": "1802"}, {"ruleId": "1803", "severity": 2, "message": "1804", "line": 82, "column": 21, "nodeType": "1801", "endLine": 93, "endColumn": 22, "suppressions": "1805"}, {"ruleId": "1799", "severity": 2, "message": "1800", "line": 106, "column": 17, "nodeType": "1801", "endLine": 109, "endColumn": 18, "suppressions": "1806"}, {"ruleId": "1803", "severity": 2, "message": "1804", "line": 106, "column": 17, "nodeType": "1801", "endLine": 109, "endColumn": 18, "suppressions": "1807"}, {"ruleId": "1764", "severity": 2, "message": "1808", "line": 62, "column": 5, "nodeType": null, "messageId": "1766", "endLine": 62, "endColumn": 7, "suppressions": "1809"}, {"ruleId": "1810", "severity": 2, "message": "1811", "line": 140, "column": 7, "nodeType": "1801", "endLine": 150, "endColumn": 8, "suppressions": "1812"}, {"ruleId": "1813", "severity": 2, "message": "1814", "line": 142, "column": 9, "nodeType": "1815", "endLine": 142, "endColumn": 21, "suppressions": "1816"}, {"ruleId": "1764", "severity": 2, "message": "1817", "line": 17, "column": 94, "nodeType": null, "messageId": "1766", "endLine": 17, "endColumn": 96, "suppressions": "1818"}, {"ruleId": "1788", "severity": 2, "message": "1819", "line": 26, "column": 19, "nodeType": "1820", "endLine": 26, "endColumn": 43, "suppressions": "1821"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 52, "column": 16, "nodeType": "1782", "messageId": "1783", "endLine": 52, "endColumn": 21, "suppressions": "1822"}, {"ruleId": "1823", "severity": 2, "message": "1824", "line": 28, "column": 6, "nodeType": "1825", "endLine": 28, "endColumn": 20, "suggestions": "1826", "suppressions": "1827"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 30, "column": 30, "nodeType": "1782", "messageId": "1783", "endLine": 30, "endColumn": 35, "suppressions": "1828"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 25, "column": 20, "nodeType": "1782", "messageId": "1783", "endLine": 25, "endColumn": 25, "suppressions": "1829"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 48, "column": 24, "nodeType": "1782", "messageId": "1783", "endLine": 48, "endColumn": 33, "suppressions": "1830"}, {"ruleId": "1831", "severity": 2, "message": "1832", "line": 15, "column": 30, "nodeType": "1820", "messageId": "1833", "endLine": 15, "endColumn": 43, "suppressions": "1834"}, {"ruleId": "1831", "severity": 2, "message": "1832", "line": 18, "column": 11, "nodeType": "1820", "messageId": "1833", "endLine": 18, "endColumn": 28, "suppressions": "1835"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 62, "column": 37, "nodeType": "1782", "messageId": "1783", "endLine": 62, "endColumn": 42, "suppressions": "1836"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 87, "column": 24, "nodeType": "1782", "messageId": "1783", "endLine": 87, "endColumn": 33, "suppressions": "1837"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 51, "column": 34, "nodeType": "1782", "messageId": "1783", "endLine": 51, "endColumn": 39, "suppressions": "1838"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 38, "column": 31, "nodeType": "1782", "messageId": "1783", "endLine": 38, "endColumn": 36, "suppressions": "1839"}, {"ruleId": "1799", "severity": 2, "message": "1800", "line": 54, "column": 9, "nodeType": "1801", "endLine": 62, "endColumn": 10, "suppressions": "1840"}, {"ruleId": "1810", "severity": 2, "message": "1811", "line": 54, "column": 9, "nodeType": "1801", "endLine": 62, "endColumn": 10, "suppressions": "1841"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 69, "column": 42, "nodeType": "1782", "messageId": "1783", "endLine": 69, "endColumn": 47, "suppressions": "1842"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 72, "column": 28, "nodeType": "1782", "messageId": "1783", "endLine": 72, "endColumn": 40, "suppressions": "1843"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 82, "column": 40, "nodeType": "1782", "messageId": "1783", "endLine": 82, "endColumn": 45, "suppressions": "1844"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 83, "column": 38, "nodeType": "1782", "messageId": "1783", "endLine": 83, "endColumn": 43, "suppressions": "1845"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 33, "column": 38, "nodeType": "1782", "messageId": "1783", "endLine": 33, "endColumn": 43, "suppressions": "1846"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 45, "column": 20, "nodeType": "1782", "messageId": "1783", "endLine": 45, "endColumn": 25, "suppressions": "1847"}, {"ruleId": "1848", "severity": 2, "message": "1849", "line": 27, "column": 7, "nodeType": "1801", "endLine": 34, "endColumn": 8, "suppressions": "1850"}, {"ruleId": "1799", "severity": 2, "message": "1800", "line": 23, "column": 7, "nodeType": "1801", "endLine": 23, "endColumn": 89, "suppressions": "1851"}, {"ruleId": "1803", "severity": 2, "message": "1804", "line": 23, "column": 7, "nodeType": "1801", "endLine": 23, "endColumn": 89, "suppressions": "1852"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 84, "column": 26, "nodeType": "1782", "messageId": "1783", "endLine": 84, "endColumn": 31, "suppressions": "1853"}, {"ruleId": "1854", "severity": 2, "message": "1855", "line": 48, "column": 13, "nodeType": null, "messageId": "1856", "endLine": 48, "endColumn": 71, "fix": "1857", "suppressions": "1858"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 90, "column": 22, "nodeType": "1782", "messageId": "1783", "endLine": 90, "endColumn": 27, "suppressions": "1859"}, {"ruleId": "1764", "severity": 2, "message": "1860", "line": 18, "column": 25, "nodeType": null, "messageId": "1766", "endLine": 18, "endColumn": 38, "suppressions": "1861"}, {"ruleId": "1862", "severity": 2, "message": "1863", "line": 13, "column": 18, "nodeType": "1864", "messageId": "1865", "endLine": 13, "endColumn": 27, "suppressions": "1866"}, {"ruleId": "1862", "severity": 2, "message": "1867", "line": 13, "column": 29, "nodeType": "1864", "messageId": "1865", "endLine": 13, "endColumn": 31, "suppressions": "1868"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 29, "column": 16, "nodeType": "1782", "messageId": "1783", "endLine": 29, "endColumn": 21, "suppressions": "1869"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 19, "column": 32, "nodeType": "1782", "messageId": "1783", "endLine": 19, "endColumn": 42, "suppressions": "1870"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 22, "column": 41, "nodeType": "1782", "messageId": "1783", "endLine": 22, "endColumn": 50, "suppressions": "1871"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 32, "column": 18, "nodeType": "1782", "messageId": "1783", "endLine": 32, "endColumn": 23, "suppressions": "1872"}, {"ruleId": "1873", "severity": 2, "message": "1874", "line": 109, "column": 49, "nodeType": "1782", "messageId": "1875", "endLine": 109, "endColumn": 54, "suppressions": "1876"}, {"ruleId": "1877", "severity": 2, "message": "1878", "line": 17, "column": 9, "nodeType": null, "messageId": "1879", "endLine": 17, "endColumn": 15, "suppressions": "1880"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 20, "column": 55, "nodeType": "1782", "messageId": "1783", "endLine": 20, "endColumn": 60, "suppressions": "1881"}, {"ruleId": "1882", "severity": 2, "message": "1883", "line": 39, "column": 3, "nodeType": null, "messageId": "1884", "endLine": 39, "endColumn": 9, "suppressions": "1885"}, {"ruleId": "1776", "severity": 2, "message": "1886", "line": 132, "column": 19, "nodeType": "1778", "endLine": 132, "endColumn": 36, "suppressions": "1887"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 158, "column": 23, "nodeType": "1782", "messageId": "1783", "endLine": 158, "endColumn": 28, "suppressions": "1888"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 32, "column": 20, "nodeType": "1889", "messageId": "1783", "endLine": 32, "endColumn": 42, "suppressions": "1890"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 39, "column": 26, "nodeType": "1782", "messageId": "1783", "endLine": 39, "endColumn": 35, "suppressions": "1891"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 56, "column": 27, "nodeType": "1889", "messageId": "1783", "endLine": 56, "endColumn": 46, "suppressions": "1892"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 60, "column": 59, "nodeType": "1782", "messageId": "1783", "endLine": 60, "endColumn": 68, "suppressions": "1893"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 87, "column": 18, "nodeType": "1889", "messageId": "1783", "endLine": 87, "endColumn": 47, "suppressions": "1894"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 94, "column": 24, "nodeType": "1782", "messageId": "1783", "endLine": 94, "endColumn": 33, "suppressions": "1895"}, {"ruleId": "1823", "severity": 2, "message": "1896", "line": 49, "column": 6, "nodeType": "1825", "endLine": 49, "endColumn": 16, "suggestions": "1897", "suppressions": "1898"}, {"ruleId": "1862", "severity": 2, "message": "1863", "line": 11, "column": 3, "nodeType": "1864", "messageId": "1865", "endLine": 11, "endColumn": 12, "suppressions": "1899"}, {"ruleId": "1862", "severity": 2, "message": "1900", "line": 20, "column": 37, "nodeType": "1782", "messageId": "1865", "endLine": 20, "endColumn": 45, "suppressions": "1901"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 46, "column": 20, "nodeType": "1782", "messageId": "1783", "endLine": 46, "endColumn": 25, "suppressions": "1902"}, {"ruleId": "1776", "severity": 2, "message": "1903", "line": 20, "column": 10, "nodeType": "1778", "endLine": 22, "endColumn": 9, "suppressions": "1904"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 64, "column": 24, "nodeType": "1782", "messageId": "1783", "endLine": 64, "endColumn": 29, "suppressions": "1905"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 38, "column": 18, "nodeType": "1782", "messageId": "1783", "endLine": 38, "endColumn": 23, "suppressions": "1906"}, {"ruleId": "1907", "severity": 2, "message": "1908", "line": 111, "column": 5, "nodeType": "1909", "messageId": "1910", "endLine": 111, "endColumn": 43, "suppressions": "1911"}, {"ruleId": "1912", "severity": 1, "message": "1913", "line": 48, "column": 34, "nodeType": "1914", "messageId": "1915", "endLine": 48, "endColumn": 43}, {"ruleId": "1912", "severity": 1, "message": "1913", "line": 67, "column": 38, "nodeType": "1914", "messageId": "1915", "endLine": 67, "endColumn": 47}, {"ruleId": "1912", "severity": 1, "message": "1913", "line": 83, "column": 39, "nodeType": "1914", "messageId": "1915", "endLine": 83, "endColumn": 48}, {"ruleId": "1912", "severity": 1, "message": "1913", "line": 106, "column": 33, "nodeType": "1914", "messageId": "1915", "endLine": 106, "endColumn": 42}, {"ruleId": "1912", "severity": 1, "message": "1913", "line": 132, "column": 34, "nodeType": "1914", "messageId": "1915", "endLine": 132, "endColumn": 43}, {"ruleId": "1912", "severity": 1, "message": "1913", "line": 172, "column": 29, "nodeType": "1914", "messageId": "1915", "endLine": 172, "endColumn": 38}, {"ruleId": "1912", "severity": 1, "message": "1913", "line": 208, "column": 27, "nodeType": "1914", "messageId": "1915", "endLine": 208, "endColumn": 36}, {"ruleId": "1873", "severity": 2, "message": "1916", "line": 36, "column": 3, "nodeType": "1782", "messageId": "1875", "endLine": 36, "endColumn": 18, "suppressions": "1917"}, {"ruleId": "1764", "severity": 2, "message": "1918", "line": 68, "column": 4, "nodeType": null, "messageId": "1766", "endLine": 68, "endColumn": 6, "suppressions": "1919"}, {"ruleId": "1920", "severity": 2, "message": "1921", "line": 95, "column": 5, "nodeType": "1864", "messageId": "1922", "endLine": 95, "endColumn": 70, "suppressions": "1923"}, {"ruleId": "1920", "severity": 2, "message": "1921", "line": 120, "column": 5, "nodeType": "1864", "messageId": "1922", "endLine": 120, "endColumn": 78, "suppressions": "1924"}, {"ruleId": "1925", "severity": 2, "message": "1926", "line": 175, "column": 44, "nodeType": "1889", "messageId": "1927", "endLine": 175, "endColumn": 66, "suppressions": "1928"}, {"ruleId": "1776", "severity": 2, "message": "1929", "line": 10, "column": 68, "nodeType": "1778", "endLine": 12, "endColumn": 7, "suppressions": "1930"}, {"ruleId": "1776", "severity": 2, "message": "1931", "line": 14, "column": 60, "nodeType": "1778", "endLine": 16, "endColumn": 7, "suppressions": "1932"}, {"ruleId": "1776", "severity": 2, "message": "1933", "line": 23, "column": 40, "nodeType": "1778", "endLine": 24, "endColumn": 18, "suppressions": "1934"}, {"ruleId": "1776", "severity": 2, "message": "1935", "line": 26, "column": 53, "nodeType": "1778", "endLine": 28, "endColumn": 9, "suppressions": "1936"}, {"ruleId": "1776", "severity": 2, "message": "1937", "line": 29, "column": 51, "nodeType": "1778", "endLine": 31, "endColumn": 9, "suppressions": "1938"}, {"ruleId": "1776", "severity": 2, "message": "1939", "line": 32, "column": 64, "nodeType": "1778", "endLine": 34, "endColumn": 9, "suppressions": "1940"}, {"ruleId": "1776", "severity": 2, "message": "1941", "line": 45, "column": 67, "nodeType": "1778", "endLine": 47, "endColumn": 9, "suppressions": "1942"}, {"ruleId": "1776", "severity": 2, "message": "1943", "line": 48, "column": 80, "nodeType": "1778", "endLine": 50, "endColumn": 9, "suppressions": "1944"}, {"ruleId": "1776", "severity": 2, "message": "1945", "line": 51, "column": 63, "nodeType": "1778", "endLine": 53, "endColumn": 9, "suppressions": "1946"}, {"ruleId": "1776", "severity": 2, "message": "1947", "line": 54, "column": 76, "nodeType": "1778", "endLine": 56, "endColumn": 9, "suppressions": "1948"}, {"ruleId": "1776", "severity": 2, "message": "1949", "line": 57, "column": 90, "nodeType": "1778", "endLine": 59, "endColumn": 9, "suppressions": "1950"}, {"ruleId": "1776", "severity": 2, "message": "1951", "line": 65, "column": 10, "nodeType": "1778", "endLine": 67, "endColumn": 9, "suppressions": "1952"}, {"ruleId": "1776", "severity": 2, "message": "1953", "line": 68, "column": 101, "nodeType": "1778", "endLine": 70, "endColumn": 9, "suppressions": "1954"}, {"ruleId": "1776", "severity": 2, "message": "1955", "line": 77, "column": 10, "nodeType": "1778", "endLine": 79, "endColumn": 9, "suppressions": "1956"}, {"ruleId": "1776", "severity": 2, "message": "1957", "line": 84, "column": 44, "nodeType": "1778", "endLine": 85, "endColumn": 20, "suppressions": "1958"}, {"ruleId": "1776", "severity": 2, "message": "1959", "line": 87, "column": 57, "nodeType": "1778", "endLine": 89, "endColumn": 11, "suppressions": "1960"}, {"ruleId": "1776", "severity": 2, "message": "1961", "line": 90, "column": 55, "nodeType": "1778", "endLine": 92, "endColumn": 11, "suppressions": "1962"}, {"ruleId": "1776", "severity": 2, "message": "1963", "line": 93, "column": 68, "nodeType": "1778", "endLine": 95, "endColumn": 11, "suppressions": "1964"}, {"ruleId": "1776", "severity": 2, "message": "1965", "line": 108, "column": 71, "nodeType": "1778", "endLine": 110, "endColumn": 11, "suppressions": "1966"}, {"ruleId": "1776", "severity": 2, "message": "1967", "line": 111, "column": 84, "nodeType": "1778", "endLine": 113, "endColumn": 11, "suppressions": "1968"}, {"ruleId": "1776", "severity": 2, "message": "1969", "line": 114, "column": 67, "nodeType": "1778", "endLine": 116, "endColumn": 11, "suppressions": "1970"}, {"ruleId": "1776", "severity": 2, "message": "1971", "line": 117, "column": 80, "nodeType": "1778", "endLine": 119, "endColumn": 11, "suppressions": "1972"}, {"ruleId": "1776", "severity": 2, "message": "1973", "line": 120, "column": 94, "nodeType": "1778", "endLine": 122, "endColumn": 11, "suppressions": "1974"}, {"ruleId": "1776", "severity": 2, "message": "1975", "line": 128, "column": 12, "nodeType": "1778", "endLine": 130, "endColumn": 11, "suppressions": "1976"}, {"ruleId": "1776", "severity": 2, "message": "1977", "line": 136, "column": 12, "nodeType": "1778", "endLine": 138, "endColumn": 11, "suppressions": "1978"}, {"ruleId": "1776", "severity": 2, "message": "1979", "line": 145, "column": 12, "nodeType": "1778", "endLine": 147, "endColumn": 11, "suppressions": "1980"}, {"ruleId": "1776", "severity": 2, "message": "1981", "line": 152, "column": 40, "nodeType": "1778", "endLine": 153, "endColumn": 18, "suppressions": "1982"}, {"ruleId": "1776", "severity": 2, "message": "1983", "line": 155, "column": 53, "nodeType": "1778", "endLine": 157, "endColumn": 9, "suppressions": "1984"}, {"ruleId": "1776", "severity": 2, "message": "1985", "line": 158, "column": 51, "nodeType": "1778", "endLine": 160, "endColumn": 9, "suppressions": "1986"}, {"ruleId": "1776", "severity": 2, "message": "1987", "line": 161, "column": 64, "nodeType": "1778", "endLine": 163, "endColumn": 9, "suppressions": "1988"}, {"ruleId": "1776", "severity": 2, "message": "1989", "line": 174, "column": 67, "nodeType": "1778", "endLine": 176, "endColumn": 9, "suppressions": "1990"}, {"ruleId": "1776", "severity": 2, "message": "1991", "line": 177, "column": 80, "nodeType": "1778", "endLine": 179, "endColumn": 9, "suppressions": "1992"}, {"ruleId": "1776", "severity": 2, "message": "1993", "line": 180, "column": 63, "nodeType": "1778", "endLine": 182, "endColumn": 9, "suppressions": "1994"}, {"ruleId": "1776", "severity": 2, "message": "1995", "line": 183, "column": 76, "nodeType": "1778", "endLine": 185, "endColumn": 9, "suppressions": "1996"}, {"ruleId": "1776", "severity": 2, "message": "1997", "line": 186, "column": 90, "nodeType": "1778", "endLine": 188, "endColumn": 9, "suppressions": "1998"}, {"ruleId": "1776", "severity": 2, "message": "1999", "line": 195, "column": 10, "nodeType": "1778", "endLine": 197, "endColumn": 9, "suppressions": "2000"}, {"ruleId": "1776", "severity": 2, "message": "2001", "line": 198, "column": 101, "nodeType": "1778", "endLine": 200, "endColumn": 9, "suppressions": "2002"}, {"ruleId": "1776", "severity": 2, "message": "2003", "line": 207, "column": 10, "nodeType": "1778", "endLine": 209, "endColumn": 9, "suppressions": "2004"}, {"ruleId": "1776", "severity": 2, "message": "2005", "line": 218, "column": 12, "nodeType": "1778", "endLine": 221, "endColumn": 7, "suppressions": "2006"}, {"ruleId": "1776", "severity": 2, "message": "2007", "line": 222, "column": 12, "nodeType": "1778", "endLine": 225, "endColumn": 7, "suppressions": "2008"}, {"ruleId": "1776", "severity": 2, "message": "2009", "line": 226, "column": 12, "nodeType": "1778", "endLine": 229, "endColumn": 7, "suppressions": "2010"}, {"ruleId": "1776", "severity": 2, "message": "2011", "line": 230, "column": 12, "nodeType": "1778", "endLine": 230, "endColumn": 33, "suppressions": "2012"}, {"ruleId": "1776", "severity": 2, "message": "2013", "line": 234, "column": 41, "nodeType": "1778", "endLine": 236, "endColumn": 9, "suppressions": "2014"}, {"ruleId": "1776", "severity": 2, "message": "2015", "line": 237, "column": 54, "nodeType": "1778", "endLine": 239, "endColumn": 9, "suppressions": "2016"}, {"ruleId": "1776", "severity": 2, "message": "2017", "line": 240, "column": 61, "nodeType": "1778", "endLine": 242, "endColumn": 9, "suppressions": "2018"}, {"ruleId": "1776", "severity": 2, "message": "2019", "line": 243, "column": 74, "nodeType": "1778", "endLine": 245, "endColumn": 9, "suppressions": "2020"}, {"ruleId": "1776", "severity": 2, "message": "2021", "line": 248, "column": 42, "nodeType": "1778", "endLine": 250, "endColumn": 9, "suppressions": "2022"}, {"ruleId": "1776", "severity": 2, "message": "2023", "line": 251, "column": 55, "nodeType": "1778", "endLine": 253, "endColumn": 9, "suppressions": "2024"}, {"ruleId": "1776", "severity": 2, "message": "2025", "line": 254, "column": 62, "nodeType": "1778", "endLine": 256, "endColumn": 9, "suppressions": "2026"}, {"ruleId": "1776", "severity": 2, "message": "2027", "line": 257, "column": 75, "nodeType": "1778", "endLine": 259, "endColumn": 9, "suppressions": "2028"}, {"ruleId": "1776", "severity": 2, "message": "2029", "line": 262, "column": 43, "nodeType": "1778", "endLine": 264, "endColumn": 9, "suppressions": "2030"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 31, "column": 30, "nodeType": "1782", "messageId": "1783", "endLine": 31, "endColumn": 35, "suppressions": "2031"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 29, "column": 40, "nodeType": "1782", "messageId": "1783", "endLine": 29, "endColumn": 45, "suppressions": "2032"}, {"ruleId": "1776", "severity": 2, "message": "2033", "line": 11, "column": 12, "nodeType": "1778", "endLine": 13, "endColumn": 41, "suppressions": "2034"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 27, "column": 27, "nodeType": "1820", "messageId": "1771", "endLine": 27, "endColumn": 38, "suppressions": "2037"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 32, "column": 27, "nodeType": "1820", "messageId": "1771", "endLine": 32, "endColumn": 38, "suppressions": "2038"}, {"ruleId": "1776", "severity": 2, "message": "2039", "line": 65, "column": 63, "nodeType": "1778", "endLine": 65, "endColumn": 74, "suppressions": "2040"}, {"ruleId": "1776", "severity": 2, "message": "2041", "line": 67, "column": 42, "nodeType": "1778", "endLine": 67, "endColumn": 69, "suppressions": "2042"}, {"ruleId": "1776", "severity": 2, "message": "2043", "line": 69, "column": 18, "nodeType": "1778", "endLine": 69, "endColumn": 26, "suppressions": "2044"}, {"ruleId": "1776", "severity": 2, "message": "2045", "line": 69, "column": 30, "nodeType": "1778", "endLine": 71, "endColumn": 13, "suppressions": "2046"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 22, "column": 5, "nodeType": "1820", "messageId": "1771", "endLine": 22, "endColumn": 16, "suggestions": "2047", "suppressions": "2048"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 30, "column": 5, "nodeType": "1820", "messageId": "1771", "endLine": 30, "endColumn": 16, "suggestions": "2049"}, {"ruleId": "2050", "severity": 2, "message": "2051", "line": 19, "column": 67, "nodeType": "2052", "messageId": "2053", "endLine": 19, "endColumn": 69, "suppressions": "2054"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 46, "column": 5, "nodeType": "1820", "messageId": "1771", "endLine": 46, "endColumn": 16, "suggestions": "2055", "suppressions": "2056"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 21, "column": 7, "nodeType": "1820", "messageId": "1771", "endLine": 21, "endColumn": 18, "suggestions": "2057", "suppressions": "2058"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 27, "column": 7, "nodeType": "1820", "messageId": "1771", "endLine": 27, "endColumn": 18, "suggestions": "2059", "suppressions": "2060"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 32, "column": 5, "nodeType": "1820", "messageId": "1771", "endLine": 32, "endColumn": 16, "suggestions": "2061", "suppressions": "2062"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 37, "column": 5, "nodeType": "1820", "messageId": "1771", "endLine": 37, "endColumn": 16, "suggestions": "2063", "suppressions": "2064"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 36, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 36, "endColumn": 14, "suggestions": "2065", "suppressions": "2066"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 48, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 48, "endColumn": 14, "suggestions": "2067", "suppressions": "2068"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 38, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 38, "endColumn": 14, "suggestions": "2069", "suppressions": "2070"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 50, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 50, "endColumn": 14, "suggestions": "2071", "suppressions": "2072"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 30, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 30, "endColumn": 14, "suggestions": "2073", "suppressions": "2074"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 39, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 39, "endColumn": 14, "suggestions": "2075", "suppressions": "2076"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 51, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 51, "endColumn": 14, "suggestions": "2077", "suppressions": "2078"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 29, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 29, "endColumn": 14, "suggestions": "2079", "suppressions": "2080"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 24, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 24, "endColumn": 14, "suggestions": "2081", "suppressions": "2082"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 47, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 47, "endColumn": 14, "suggestions": "2083", "suppressions": "2084"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 60, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 60, "endColumn": 14, "suggestions": "2085", "suppressions": "2086"}, {"ruleId": "2087", "severity": 2, "message": "2088", "line": 33, "column": 16, "nodeType": "1815", "messageId": "2089", "endLine": 33, "endColumn": 19, "suppressions": "2090"}, {"ruleId": "2087", "severity": 2, "message": "2091", "line": 33, "column": 20, "nodeType": "1815", "messageId": "2089", "endLine": 33, "endColumn": 26, "suppressions": "2092"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 18, "column": 5, "nodeType": "1820", "messageId": "1771", "endLine": 18, "endColumn": 16, "suggestions": "2093", "suppressions": "2094"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 20, "column": 5, "nodeType": "1820", "messageId": "1771", "endLine": 20, "endColumn": 16, "suggestions": "2095", "suppressions": "2096"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 41, "column": 5, "nodeType": "1820", "messageId": "1771", "endLine": 41, "endColumn": 16, "suggestions": "2097", "suppressions": "2098"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 38, "column": 5, "nodeType": "1820", "messageId": "1771", "endLine": 38, "endColumn": 16, "suggestions": "2099", "suppressions": "2100"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 6, "column": 1, "nodeType": "1820", "messageId": "1771", "endLine": 6, "endColumn": 12, "suggestions": "2101", "suppressions": "2102"}, {"ruleId": "2087", "severity": 2, "message": "2088", "line": 76, "column": 12, "nodeType": "1815", "messageId": "2089", "endLine": 76, "endColumn": 15, "suppressions": "2103"}, {"ruleId": "2087", "severity": 2, "message": "2091", "line": 76, "column": 16, "nodeType": "1815", "messageId": "2089", "endLine": 76, "endColumn": 22, "suppressions": "2104"}, {"ruleId": "1873", "severity": 2, "message": "2105", "line": 23, "column": 7, "nodeType": "1782", "messageId": "1875", "endLine": 23, "endColumn": 21, "suppressions": "2106"}, {"ruleId": "2107", "severity": 2, "message": "2108", "line": 31, "column": 27, "nodeType": "2109", "messageId": "1771", "endLine": 31, "endColumn": 38, "suppressions": "2110"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 61, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 61, "endColumn": 14, "suggestions": "2111", "suppressions": "2112"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 62, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 62, "endColumn": 14, "suggestions": "2113", "suppressions": "2114"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 63, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 63, "endColumn": 14, "suggestions": "2115", "suppressions": "2116"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 64, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 64, "endColumn": 14, "suggestions": "2117", "suppressions": "2118"}, {"ruleId": "1788", "severity": 2, "message": "2119", "line": 99, "column": 11, "nodeType": "1820", "endLine": 99, "endColumn": 32, "suppressions": "2120"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 103, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 103, "endColumn": 14, "suggestions": "2121", "suppressions": "2122"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 104, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 104, "endColumn": 14, "suggestions": "2123", "suppressions": "2124"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 105, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 105, "endColumn": 14, "suggestions": "2125", "suppressions": "2126"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 106, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 106, "endColumn": 14, "suggestions": "2127", "suppressions": "2128"}, {"ruleId": "1764", "severity": 2, "message": "1765", "line": 114, "column": 23, "nodeType": null, "messageId": "1766", "endLine": 114, "endColumn": 43, "suppressions": "2129"}, {"ruleId": "1877", "severity": 2, "message": "1878", "line": 132, "column": 3, "nodeType": null, "messageId": "1879", "endLine": 132, "endColumn": 9, "suppressions": "2130"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 145, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 145, "endColumn": 14, "suggestions": "2131", "suppressions": "2132"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 147, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 147, "endColumn": 14, "suggestions": "2133", "suppressions": "2134"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 148, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 148, "endColumn": 14, "suggestions": "2135", "suppressions": "2136"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 151, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 151, "endColumn": 14, "suggestions": "2137", "suppressions": "2138"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 185, "column": 7, "nodeType": "1820", "messageId": "1771", "endLine": 185, "endColumn": 18, "suggestions": "2139", "suppressions": "2140"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 186, "column": 7, "nodeType": "1820", "messageId": "1771", "endLine": 186, "endColumn": 18, "suggestions": "2141", "suppressions": "2142"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 187, "column": 7, "nodeType": "1820", "messageId": "1771", "endLine": 187, "endColumn": 18, "suggestions": "2143", "suppressions": "2144"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 188, "column": 7, "nodeType": "1820", "messageId": "1771", "endLine": 188, "endColumn": 18, "suggestions": "2145", "suppressions": "2146"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 189, "column": 7, "nodeType": "1820", "messageId": "1771", "endLine": 189, "endColumn": 18, "suggestions": "2147", "suppressions": "2148"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 190, "column": 7, "nodeType": "1820", "messageId": "1771", "endLine": 190, "endColumn": 18, "suggestions": "2149", "suppressions": "2150"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 192, "column": 7, "nodeType": "1820", "messageId": "1771", "endLine": 192, "endColumn": 18, "suggestions": "2151", "suppressions": "2152"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 198, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 198, "endColumn": 14, "suggestions": "2153", "suppressions": "2154"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 199, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 199, "endColumn": 14, "suggestions": "2155", "suppressions": "2156"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 202, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 202, "endColumn": 14, "suggestions": "2157", "suppressions": "2158"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 205, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 205, "endColumn": 14, "suggestions": "2159", "suppressions": "2160"}, {"ruleId": "2161", "severity": 2, "message": "2162", "line": 17, "column": 12, "nodeType": "1782", "messageId": "2163", "endLine": 17, "endColumn": 19, "suppressions": "2164"}, {"ruleId": "2161", "severity": 2, "message": "2162", "line": 17, "column": 46, "nodeType": "1782", "messageId": "2163", "endLine": 17, "endColumn": 53, "suppressions": "2165"}, {"ruleId": "2161", "severity": 2, "message": "2162", "line": 33, "column": 34, "nodeType": "1782", "messageId": "2163", "endLine": 33, "endColumn": 41, "suppressions": "2166"}, {"ruleId": "1823", "severity": 2, "message": "2167", "line": 61, "column": 6, "nodeType": "1825", "endLine": 61, "endColumn": 20, "suggestions": "2168", "suppressions": "2169"}, {"ruleId": "1823", "severity": 2, "message": "2170", "line": 71, "column": 6, "nodeType": "1825", "endLine": 71, "endColumn": 28, "suggestions": "2171", "suppressions": "2172"}, {"ruleId": "1823", "severity": 2, "message": "2173", "line": 78, "column": 6, "nodeType": "1825", "endLine": 78, "endColumn": 14, "suggestions": "2174", "suppressions": "2175"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 38, "column": 29, "nodeType": "1782", "messageId": "1783", "endLine": 38, "endColumn": 34, "suppressions": "2176"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 63, "column": 36, "nodeType": "1782", "messageId": "1783", "endLine": 63, "endColumn": 41, "suppressions": "2177"}, {"ruleId": "1776", "severity": 2, "message": "2178", "line": 53, "column": 65, "nodeType": "1778", "endLine": 54, "endColumn": 58, "suppressions": "2179"}, {"ruleId": "1776", "severity": 2, "message": "2178", "line": 55, "column": 74, "nodeType": "1778", "endLine": 55, "endColumn": 78, "suppressions": "2180"}, {"ruleId": "1776", "severity": 2, "message": "2181", "line": 59, "column": 65, "nodeType": "1778", "endLine": 60, "endColumn": 27, "suppressions": "2182"}, {"ruleId": "1776", "severity": 2, "message": "2183", "line": 69, "column": 83, "nodeType": "1778", "endLine": 70, "endColumn": 31, "suppressions": "2184"}, {"ruleId": "1776", "severity": 2, "message": "2183", "line": 73, "column": 20, "nodeType": "1778", "endLine": 74, "endColumn": 28, "suppressions": "2185"}, {"ruleId": "1776", "severity": 2, "message": "2186", "line": 88, "column": 65, "nodeType": "1778", "endLine": 89, "endColumn": 58, "suppressions": "2187"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 99, "column": 22, "nodeType": "1782", "messageId": "1783", "endLine": 99, "endColumn": 27, "suppressions": "2188"}, {"ruleId": "2050", "severity": 2, "message": "2189", "line": 55, "column": 16, "nodeType": "2052", "messageId": "2053", "endLine": 55, "endColumn": 18, "suppressions": "2190"}, {"ruleId": "2050", "severity": 2, "message": "2189", "line": 66, "column": 16, "nodeType": "2052", "messageId": "2053", "endLine": 66, "endColumn": 18, "suppressions": "2191"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 93, "column": 88, "nodeType": "1782", "messageId": "1783", "endLine": 93, "endColumn": 93, "suppressions": "2192"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 44, "column": 20, "nodeType": "1782", "messageId": "1783", "endLine": 44, "endColumn": 25, "suppressions": "2193"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 79, "column": 22, "nodeType": "1782", "messageId": "1783", "endLine": 79, "endColumn": 27, "suppressions": "2194"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 94, "column": 24, "nodeType": "1782", "messageId": "1783", "endLine": 94, "endColumn": 29, "suppressions": "2195"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 57, "column": 20, "nodeType": "1782", "messageId": "1783", "endLine": 57, "endColumn": 25, "suppressions": "2196"}, {"ruleId": "2161", "severity": 2, "message": "2197", "line": 33, "column": 15, "nodeType": "1782", "messageId": "2163", "endLine": 33, "endColumn": 21, "suppressions": "2198"}, {"ruleId": "2161", "severity": 2, "message": "2197", "line": 35, "column": 15, "nodeType": "1782", "messageId": "2163", "endLine": 35, "endColumn": 21, "suppressions": "2199"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 83, "column": 66, "nodeType": "1782", "messageId": "1783", "endLine": 83, "endColumn": 71, "suppressions": "2200"}, {"ruleId": "2035", "severity": 1, "message": "2036", "line": 51, "column": 3, "nodeType": "1820", "messageId": "1771", "endLine": 51, "endColumn": 14, "suggestions": "2201", "suppressions": "2202"}, {"ruleId": "1862", "severity": 2, "message": "2203", "line": 4, "column": 20, "nodeType": "1864", "messageId": "1865", "endLine": 4, "endColumn": 30, "suppressions": "2204"}, {"ruleId": "2205", "severity": 2, "message": "2206", "line": 151, "column": 13, "nodeType": "1820", "messageId": "2207", "endLine": 151, "endColumn": 22, "suppressions": "2208"}, {"ruleId": "2205", "severity": 2, "message": "2206", "line": 153, "column": 46, "nodeType": "1820", "messageId": "2207", "endLine": 153, "endColumn": 55, "suppressions": "2209"}, {"ruleId": "2205", "severity": 2, "message": "2206", "line": 154, "column": 52, "nodeType": "1820", "messageId": "2207", "endLine": 154, "endColumn": 61, "suppressions": "2210"}, {"ruleId": "1907", "severity": 2, "message": "2211", "line": 19, "column": 9, "nodeType": "2212", "messageId": "1910", "endLine": 19, "endColumn": 49, "fix": "2213", "suppressions": "2214"}, {"ruleId": "1780", "severity": 2, "message": "1781", "line": 55, "column": 36, "nodeType": "1782", "messageId": "1783", "endLine": 55, "endColumn": 41, "suppressions": "2215"}, "sonarjs/cognitive-complexity", "Refactor this function to reduce its Cognitive Complexity from 17 to the 15 allowed.", "refactorFunction", ["2216"], "no-empty", "Empty block statement.", "BlockStatement", "unexpected", ["2217"], ["2218"], "Refactor this function to reduce its Cognitive Complexity from 19 to the 15 allowed.", ["2219"], "i18next/no-literal-string", "disallow literal string: <MLink href={`/${alias}`} variant=\"underlined\">\r\n          bratislava.sk/{alias}\r\n        </MLink>", "JSXText", ["2220"], "react/no-array-index-key", "Do not use Array index in keys", "Identifier", "noArrayIndex", ["2221"], ["2222"], "Refactor this function to reduce its Cognitive Complexity from 33 to the 15 allowed.", ["2223"], "xss/no-mixed-html", "Non-HTML variable 'breakWord' is used to store raw HTML", ["2224"], ["2225"], ["2226"], ["2227"], ["2228"], ["2229"], ["2230"], ["2231"], ["2232"], "jsx-a11y/click-events-have-key-events", "Visible, non-interactive elements with click handlers must have at least one keyboard listener.", "JSXOpeningElement", ["2233"], "jsx-a11y/no-static-element-interactions", "Avoid non-native interactive elements. If using native HTML is not possible, add an appropriate role and support for tabbing, mouse, keyboard, and touch inputs to an interactive content element.", ["2234"], ["2235"], ["2236"], "Refactor this function to reduce its Cognitive Complexity from 16 to the 15 allowed.", ["2237"], "jsx-a11y/no-noninteractive-element-interactions", "Non-interactive elements should not be assigned mouse or keyboard event listeners.", ["2238"], "jsx-a11y/no-noninteractive-tabindex", "`tabIndex` should only be declared on interactive elements.", "JSXAttribute", ["2239"], "Refactor this function to reduce its Cognitive Complexity from 22 to the 15 allowed.", ["2240"], "HTML passed in to function 'inputRef.current.indexOf'", "MemberExpression", ["2241"], ["2242"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'onChange'. Either include it or remove the dependency array. If 'onChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", "ArrayExpression", ["2243"], ["2244"], ["2245"], ["2246"], ["2247"], "unicorn/consistent-destructuring", "Use destructured variables over properties.", "consistentDestructuring", ["2248"], ["2249"], ["2250"], ["2251"], ["2252"], ["2253"], ["2254"], ["2255"], ["2256"], ["2257"], ["2258"], ["2259"], ["2260"], ["2261"], "jsx-a11y/interactive-supports-focus", "Elements with the 'button' interactive role must be focusable.", ["2262"], ["2263"], ["2264"], ["2265"], "unicorn/prefer-switch", "Use `switch` instead of multiple `else-if`.", "prefer-switch", {"range": "2266", "text": "2267"}, ["2268"], ["2269"], "Refactor this function to reduce its Cognitive Complexity from 26 to the 15 allowed.", ["2270"], "react/prop-types", "'className' is missing in props validation", "Property", "missingPropType", ["2271"], "'id' is missing in props validation", ["2272"], ["2273"], ["2274"], ["2275"], ["2276"], "@typescript-eslint/no-unused-vars", "'props' is defined but never used.", "unusedVar", ["2277"], "sonarjs/no-small-switch", "\"switch\" statements should have at least 3 \"case\" clauses", "smallSwitch", ["2278"], ["2279"], "sonarjs/max-switch-cases", "Reduce the number of non-empty switch cases from 31 to at most 30.", "reduceNumberOfNonEmptySwitchCases", ["2280"], "disallow literal string: <div>Documents-section</div>", ["2281"], ["2282"], "TemplateLiteral", ["2283"], ["2284"], ["2285"], ["2286"], ["2287"], ["2288"], "React Hook useEffect has missing dependencies: 'filters', 'setFilters', and 'tagIds'. Either include them or remove the dependency array.", ["2289"], ["2290"], ["2291"], "'disabled' is missing in props validation", ["2292"], ["2293"], "disallow literal string: <MLink\r\n          variant=\"underlined\"\r\n          className=\"font-semibold\"\r\n          href=\"https://zverejnovanie.bratislava.sk\"\r\n          // eslint-disable-next-line i18next/no-literal-string\r\n        >\r\n          zverejnovanie.bratislava.sk\r\n        </MLink>", ["2294"], ["2295"], ["2296"], "prefer-destructuring", "Use array destructuring.", "AssignmentExpression", "preferDestructuring", ["2297"], "func-names", "Unexpected unnamed function.", "FunctionExpression", "unnamed", "'onLoadingChange' is defined but never used.", ["2298"], "Refactor this function to reduce its Cognitive Complexity from 35 to the 15 allowed.", ["2299"], "@tanstack/query/exhaustive-deps", "The following dependencies are missing in your queryKey: filters", "missingDeps", ["2300"], ["2301"], "sonarjs/no-nested-template-literals", "Refactor this code to not use nested template literals.", "nestedTemplateLiterals", ["2302"], "disallow literal string: <Accordion variant=\"boxed\" title=\"Accordion boxed (default)\">\r\n        Content\r\n      </Accordion>", ["2303"], "disallow literal string: <Accordion variant=\"footer\" title=\"Accordion footer\">\r\n        Content\r\n      </Accordion>", ["2304"], "disallow literal string: <Button variant={solidVariant}>\r\n          Button {variantPrefix ? `${variantPrefix}-solid` : 'solid'}\r\n        </Button>", ["2305"], "disallow literal string: <Button variant={solidVariant} size=\"small\">\r\n          Button\r\n        </Button>", ["2306"], "disallow literal string: <Button variant={solidVariant} isDisabled>\r\n          Disabled\r\n        </Button>", ["2307"], "disallow literal string: <Button variant={solidVariant} size=\"small\" isDisabled>\r\n          Disabled\r\n        </Button>", ["2308"], "disallow literal string: <Button variant={solidVariant} startIcon={<SearchIcon />}>\r\n          Button\r\n        </Button>", ["2309"], "disallow literal string: <Button variant={solidVariant} size=\"small\" startIcon={<SearchIcon />}>\r\n          Button\r\n        </Button>", ["2310"], "disallow literal string: <Button variant={solidVariant} endIcon={<EditIcon />}>\r\n          Button\r\n        </Button>", ["2311"], "disallow literal string: <Button variant={solidVariant} size=\"small\" endIcon={<EditIcon />}>\r\n          Button\r\n        </Button>", ["2312"], "disallow literal string: <Button variant={solidVariant} startIcon={<SearchIcon />} endIcon={<EditIcon />}>\r\n          Button\r\n        </Button>", ["2313"], "disallow literal string: <Button\r\n          variant={solidVariant}\r\n          size=\"small\"\r\n          startIcon={<SearchIcon />}\r\n          endIcon={<EditIcon />}\r\n        >\r\n          Button\r\n        </Button>", ["2314"], "disallow literal string: <Button variant={solidVariant} startIcon={<SearchIcon />} endIcon={<EditIcon />} isDisabled>\r\n          Disabled\r\n        </Button>", ["2315"], "disallow literal string: <Button\r\n          variant={solidVariant}\r\n          startIcon={<SearchIcon />}\r\n          endIcon={<EditIcon />}\r\n          size=\"small\"\r\n          isDisabled\r\n        >\r\n          Disabled\r\n        </Button>", ["2316"], "disallow literal string: <Button variant={outlineVariant}>\r\n            Button {variantPrefix ? `${variantPrefix}-outline` : 'outline'}\r\n          </Button>", ["2317"], "disallow literal string: <Button variant={outlineVariant} size=\"small\">\r\n            Button\r\n          </Button>", ["2318"], "disallow literal string: <Button variant={outlineVariant} isDisabled>\r\n            Disabled\r\n          </Button>", ["2319"], "disallow literal string: <Button variant={outlineVariant} size=\"small\" isDisabled>\r\n            Disabled\r\n          </Button>", ["2320"], "disallow literal string: <Button variant={outlineVariant} startIcon={<SearchIcon />}>\r\n            Button\r\n          </Button>", ["2321"], "disallow literal string: <Button variant={outlineVariant} size=\"small\" startIcon={<SearchIcon />}>\r\n            Button\r\n          </Button>", ["2322"], "disallow literal string: <Button variant={outlineVariant} endIcon={<EditIcon />}>\r\n            Button\r\n          </Button>", ["2323"], "disallow literal string: <Button variant={outlineVariant} size=\"small\" endIcon={<EditIcon />}>\r\n            Button\r\n          </Button>", ["2324"], "disallow literal string: <Button variant={outlineVariant} startIcon={<SearchIcon />} endIcon={<EditIcon />}>\r\n            Button\r\n          </Button>", ["2325"], "disallow literal string: <Button\r\n            variant={outlineVariant}\r\n            size=\"small\"\r\n            startIcon={<SearchIcon />}\r\n            endIcon={<EditIcon />}\r\n          >\r\n            Button\r\n          </Button>", ["2326"], "disallow literal string: <Button\r\n            variant={outlineVariant}\r\n            startIcon={<SearchIcon />}\r\n            endIcon={<EditIcon />}\r\n            isDisabled\r\n          >\r\n            Disabled\r\n          </Button>", ["2327"], "disallow literal string: <Button\r\n            variant={outlineVariant}\r\n            startIcon={<SearchIcon />}\r\n            endIcon={<EditIcon />}\r\n            size=\"small\"\r\n            isDisabled\r\n          >\r\n            Disabled\r\n          </Button>", ["2328"], "disallow literal string: <Button variant={plainVariant}>\r\n          Button {variantPrefix ? `${variantPrefix}-plain` : 'plain'}\r\n        </Button>", ["2329"], "disallow literal string: <Button variant={plainVariant} size=\"small\">\r\n          Button\r\n        </Button>", ["2330"], "disallow literal string: <Button variant={plainVariant} isDisabled>\r\n          Disabled\r\n        </Button>", ["2331"], "disallow literal string: <Button variant={plainVariant} size=\"small\" isDisabled>\r\n          Disabled\r\n        </Button>", ["2332"], "disallow literal string: <Button variant={plainVariant} startIcon={<SearchIcon />}>\r\n          Button\r\n        </Button>", ["2333"], "disallow literal string: <Button variant={plainVariant} size=\"small\" startIcon={<SearchIcon />}>\r\n          Button\r\n        </Button>", ["2334"], "disallow literal string: <Button variant={plainVariant} endIcon={<EditIcon />}>\r\n          Button\r\n        </Button>", ["2335"], "disallow literal string: <Button variant={plainVariant} size=\"small\" endIcon={<EditIcon />}>\r\n          Button\r\n        </Button>", ["2336"], "disallow literal string: <Button variant={plainVariant} startIcon={<SearchIcon />} endIcon={<EditIcon />}>\r\n          Button\r\n        </Button>", ["2337"], "disallow literal string: <Button\r\n          variant={plainVariant}\r\n          size=\"small\"\r\n          startIcon={<SearchIcon />}\r\n          endIcon={<EditIcon />}\r\n        >\r\n          Button\r\n        </Button>", ["2338"], "disallow literal string: <Button variant={plainVariant} startIcon={<SearchIcon />} endIcon={<EditIcon />} isDisabled>\r\n          Disabled\r\n        </Button>", ["2339"], "disallow literal string: <Button\r\n          variant={plainVariant}\r\n          size=\"small\"\r\n          startIcon={<SearchIcon />}\r\n          endIcon={<EditIcon />}\r\n          isDisabled\r\n        >\r\n          Disabled\r\n        </Button>", ["2340"], "disallow literal string: <div>\r\n        For link buttons, you can use `hasLinkIcon` to automatically add endIcon (ArrowRight or\r\n        ExternalLink icon).\r\n      </div>", ["2341"], "disallow literal string: <div>\r\n        <PERSON><PERSON><PERSON> should use `icon` and `aria-label` props instead of `children` an cannot be used\r\n        with `startIcon`, `endIcon`.\r\n      </div>", ["2342"], "disallow literal string: <div>\r\n        &quot;Naked&quot; icon buttons, e.g. close icons, calendar icon button, should have expanded\r\n        clickable/touchable area. For this case, we have `icon-wrapped` variant.\r\n      </div>", ["2343"], "disallow literal string: <div>TODO: Loading spinner</div>", ["2344"], "disallow literal string: <Button variant=\"link\" href=\"#\">\r\n          Link\r\n        </Button>", ["2345"], "disallow literal string: <Button variant=\"link\" href=\"#\" size=\"small\">\r\n          Link\r\n        </Button>", ["2346"], "disallow literal string: <Button variant=\"link\" href=\"https://bratislava.sk\">\r\n          External link\r\n        </Button>", ["2347"], "disallow literal string: <Button variant=\"link\" href=\"https://bratislava.sk\" size=\"small\">\r\n          External link\r\n        </Button>", ["2348"], "disallow literal string: <Button variant=\"solid\" href=\"#\">\r\n          Link\r\n        </Button>", ["2349"], "disallow literal string: <Button variant=\"solid\" href=\"#\" size=\"small\">\r\n          Link\r\n        </Button>", ["2350"], "disallow literal string: <Button variant=\"solid\" href=\"https://bratislava.sk\">\r\n          External link\r\n        </Button>", ["2351"], "disallow literal string: <Button variant=\"solid\" href=\"https://bratislava.sk\" size=\"small\">\r\n          External link\r\n        </Button>", ["2352"], "disallow literal string: <Button variant=\"solid\" isLoading>\r\n          This is loading button\r\n        </Button>", ["2353"], ["2354"], ["2355"], "disallow literal string: <div>\r\n        WARNING: branded colors are automatically set from primary and secondary colors from\r\n        tailwind config as its set brand{' '}\r\n      </div>", ["2356"], "no-console", "Unexpected console statement.", ["2357"], ["2358"], "disallow literal string: <h1 className=\"mb-10 text-center text-h1 underline\">Style Guide</h1>", ["2359"], "disallow literal string: <h1 className=\"ml-2 text-h2\">Change brand of Style Guide</h1>", ["2360"], "disallow literal string: <b>WARNING:</b>", ["2361"], "disallow literal string: <p className=\"ml-2\">\r\n              <b>WARNING:</b> Components should change automatically brand color scheme after we\r\n              change colors in :root based on chosen brand. Click on button for change in styleguide\r\n            </p>", ["2362"], ["2363"], ["2364"], ["2365"], "consistent-return", "Expected to return a value at the end of async arrow function.", "ArrowFunctionExpression", "missingReturn", ["2366"], ["2367"], ["2368"], ["2369"], ["2370"], ["2371"], ["2372"], ["2373"], ["2374"], ["2375"], ["2376"], ["2377"], ["2378"], ["2379"], ["2380"], ["2381"], ["2382"], ["2383"], ["2384"], ["2385"], ["2386"], ["2387"], ["2388"], ["2389"], ["2390"], ["2391"], ["2392"], ["2393"], ["2394"], ["2395"], ["2396"], ["2397"], ["2398"], "react/no-unknown-property", "Unknown property 'jsx' found", "unknownProp", ["2399"], "Unknown property 'global' found", ["2400"], ["2401"], ["2402"], ["2403"], ["2404"], ["2405"], ["2406"], ["2407"], ["2408"], ["2409"], ["2410"], ["2411"], ["2412"], "'filterMarkdown' is assigned a value but never used.", ["2413"], "no-control-regex", "Unexpected control character(s) in regular expression: \\x09.", "Literal", ["2414"], ["2415"], ["2416"], ["2417"], ["2418"], ["2419"], ["2420"], ["2421"], ["2422"], "HTML passed in to function 'section.content.split'", ["2423"], ["2424"], ["2425"], ["2426"], ["2427"], ["2428"], ["2429"], ["2430"], ["2431"], ["2432"], ["2433"], ["2434"], ["2435"], ["2436"], ["2437"], ["2438"], ["2439"], ["2440"], ["2441"], ["2442"], ["2443"], ["2444"], ["2445"], ["2446"], ["2447"], ["2448"], ["2449"], ["2450"], ["2451"], ["2452"], ["2453"], ["2454"], ["2455"], ["2456"], ["2457"], ["2458"], ["2459"], ["2460"], ["2461"], ["2462"], ["2463"], "no-restricted-globals", "Unexpected use of 'history'.", "defaultMessage", ["2464"], ["2465"], ["2466"], "React Hook useEffect has missing dependencies: 'onTagChange', 'selectedPageCategory', and 'tags'. Either include them or remove the dependency array. If 'onTagChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2467"], ["2468"], "React Hook useEffect has missing dependencies: 'defaultChip.id', 'onTagChange', and 'tags'. Either include them or remove the dependency array. If 'onTagChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2469"], ["2470"], "React Hook useEffect has a missing dependency: 'defaultChip.id'. Either include it or remove the dependency array.", ["2471"], ["2472"], ["2473"], ["2474"], "disallow literal string: <Typography variant=\"p-small\" className=\"whitespace-normal\">\r\n      Toto VZN bolo zrušené všeobecne záväzným nariadením{' '}\r\n      <RegulationMLink regNumber={cancellation?.attributes?.regNumber} /> dňa{' '}\r\n      <span className=\"font-medium\">{formatDate(effectiveUntil)}</span>.\r\n    </Typography>", ["2475"], ["2476"], "disallow literal string: <Typography variant=\"p-small\" className=\"whitespace-normal\">\r\n      Toto VZN je dodatkom{' '}\r\n      {cancelledAmendees?.map((cancelledAmendee, index) => {\r\n        return (\r\n          <Fragment key={cancelledAmendee.id}>\r\n            {index === 0\r\n              ? ' k '\r\n              : index === (cancelledAmendees?.length ?? 0) - 1\r\n                ? ' a k '\r\n                : ', k '}{' '}\r\n            <RegulationMLink regNumber={cancelledAmendee.attributes?.regNumber} />, ktoré bolo\r\n            zrušené nariadením{' '}\r\n            <RegulationMLink\r\n              regNumber={cancelledAmendee.attributes?.cancellation?.data?.attributes?.regNumber}\r\n            />{' '}\r\n            s účinnosťou od{' '}\r\n            <span className=\"font-medium whitespace-nowrap\">\r\n              {' '}\r\n              {formatDate(\r\n                amending?.find((amended) => amended.attributes?.cancellation?.data)?.attributes\r\n                  ?.cancellation?.data?.attributes?.effectiveFrom,\r\n              )}\r\n            </span>\r\n          </Fragment>\r\n        )\r\n      })}\r\n      .\r\n    </Typography>", ["2477"], "disallow literal string: <Fragment key={cancelledAmendee.id}>\r\n            {index === 0\r\n              ? ' k '\r\n              : index === (cancelledAmendees?.length ?? 0) - 1\r\n                ? ' a k '\r\n                : ', k '}{' '}\r\n            <RegulationMLink regNumber={cancelledAmendee.attributes?.regNumber} />, ktoré bolo\r\n            zrušené nariadením{' '}\r\n            <RegulationMLink\r\n              regNumber={cancelledAmendee.attributes?.cancellation?.data?.attributes?.regNumber}\r\n            />{' '}\r\n            s účinn<PERSON>ťou od{' '}\r\n            <span className=\"font-medium whitespace-nowrap\">\r\n              {' '}\r\n              {formatDate(\r\n                amending?.find((amended) => amended.attributes?.cancellation?.data)?.attributes\r\n                  ?.cancellation?.data?.attributes?.effectiveFrom,\r\n              )}\r\n            </span>\r\n          </Fragment>", ["2478"], ["2479"], "disallow literal string: <Typography variant=\"p-small\" className=\"whitespace-normal\">\r\n      Toto VZN je aktuálne platné, s dátumom účinnosti od{' '}\r\n      <span className=\"font-medium whitespace-nowrap\">\r\n        {formatDate(regulation.attributes?.effectiveFrom)}\r\n      </span>\r\n      .\r\n    </Typography>", ["2480"], ["2481"], "Expected to return a value at the end of arrow function.", ["2482"], ["2483"], ["2484"], ["2485"], ["2486"], ["2487"], ["2488"], "Unexpected use of 'screen'.", ["2489"], ["2490"], ["2491"], ["2492"], ["2493"], "'statusCode' is missing in props validation", ["2494"], "no-underscore-dangle", "Unexpected dangling '_' in '_id'.", "unexpectedUnderscore", ["2495"], ["2496"], ["2497"], "Use object destructuring.", "VariableDeclarator", {"range": "2498", "text": "2499"}, ["2500"], ["2501"], {"kind": "2502", "justification": "2503"}, {"messageId": "2504", "data": "2505", "fix": "2506", "desc": "2507"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"desc": "2508", "fix": "2509"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, [1604, 3047], "switch (type) {\n            case 'start-ellipsis': \n            case 'end-ellipsis': {\r\n              children = '…'\r\n            \n            break;\n            }\n            case 'page': {\r\n              children = (\r\n                <Button\r\n                  variant={selected ? 'solid' : 'outline'}\r\n                  isDisabled={disabled}\r\n                  onPress={onPress}\r\n                  aria-current={ariaCurrent}\r\n                  aria-label={t('Pagination.aria.goToPage', { page })}\r\n                  className=\"flex size-10 shrink-0 grow-0 items-center justify-center rounded-full lg:size-12\"\r\n                >\r\n                  {page}\r\n                </Button>\r\n              )\r\n            \n            break;\n            }\n            case 'previous': \n            case 'next': {\r\n              let icon: ReactNode\r\n              let ariaLabel = ''\r\n              if (type === 'previous') {\r\n                icon = <ArrowLeftIcon />\r\n                ariaLabel = t('Pagination.aria.goToPreviousPage')\r\n              }\r\n              if (type === 'next') {\r\n                icon = <ArrowRightIcon />\r\n                ariaLabel = t('Pagination.aria.goToNextPage')\r\n              }\r\n\r\n              children = (\r\n                <Button\r\n                  variant=\"plain\"\r\n                  isDisabled={disabled}\r\n                  onPress={onPress}\r\n                  aria-label={ariaLabel}\r\n                  icon={icon}\r\n                  className=\"rounded-full\"\r\n                />\r\n              )\r\n            \n            break;\n            }\n            // No default\n            }", {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"desc": "2510", "fix": "2511"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2513", "fix": "2514", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2516", "fix": "2517", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2518", "fix": "2519", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2520", "fix": "2521", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2522", "fix": "2523", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2524", "fix": "2525", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2526", "fix": "2527", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2528", "fix": "2529", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2530", "fix": "2531", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2532", "fix": "2533", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2534", "fix": "2535", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2536", "fix": "2537", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2538", "fix": "2539", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2540", "fix": "2541", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2542", "fix": "2543", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2544", "fix": "2545", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2546", "fix": "2547", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2548", "fix": "2549", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2550", "fix": "2551", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2552", "fix": "2553", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2554", "fix": "2555", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2556", "fix": "2557", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2558", "fix": "2559", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2560", "fix": "2561", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2562", "fix": "2563", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2564", "fix": "2565", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2566", "fix": "2567", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2568", "fix": "2569", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2570", "fix": "2571", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2572", "fix": "2573", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2574", "fix": "2575", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2576", "fix": "2577", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2578", "fix": "2579", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2580", "fix": "2581", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2582", "fix": "2583", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2584", "fix": "2585", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2586", "fix": "2587", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2588", "fix": "2589", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2590", "fix": "2591", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2592", "fix": "2593", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2594", "fix": "2595", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2596", "fix": "2597", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2598", "fix": "2599", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2600", "fix": "2601", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2602", "fix": "2603", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2604", "fix": "2605", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"desc": "2606", "fix": "2607"}, {"kind": "2502", "justification": "2503"}, {"desc": "2608", "fix": "2609"}, {"kind": "2502", "justification": "2503"}, {"desc": "2610", "fix": "2611"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"messageId": "2512", "data": "2612", "fix": "2613", "desc": "2515"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, [843, 883], "{effectiveFrom} = regulation", {"kind": "2502", "justification": "2503"}, {"kind": "2502", "justification": "2503"}, "directive", "", "suggestComment", {"type": "2614"}, {"range": "2615", "text": "2616"}, "Add comment inside empty block statement.", "Update the dependencies array to be: [onChange, selectedTags]", {"range": "2617", "text": "2618"}, "Update the dependencies array to be: [filters, setFilters, tagIds, tagsData]", {"range": "2619", "text": "2620"}, "removeConsole", {"propertyName": "2621"}, {"range": "2622", "text": "2503"}, "Remove the console.log().", {"propertyName": "2621"}, {"range": "2623", "text": "2503"}, {"propertyName": "2621"}, {"range": "2624", "text": "2503"}, {"propertyName": "2621"}, {"range": "2625", "text": "2503"}, {"propertyName": "2621"}, {"range": "2626", "text": "2503"}, {"propertyName": "2621"}, {"range": "2627", "text": "2503"}, {"propertyName": "2621"}, {"range": "2628", "text": "2503"}, {"propertyName": "2621"}, {"range": "2629", "text": "2503"}, {"propertyName": "2621"}, {"range": "2630", "text": "2503"}, {"propertyName": "2621"}, {"range": "2631", "text": "2503"}, {"propertyName": "2621"}, {"range": "2632", "text": "2503"}, {"propertyName": "2621"}, {"range": "2633", "text": "2503"}, {"propertyName": "2621"}, {"range": "2634", "text": "2503"}, {"propertyName": "2621"}, {"range": "2635", "text": "2503"}, {"propertyName": "2621"}, {"range": "2636", "text": "2503"}, {"propertyName": "2621"}, {"range": "2637", "text": "2503"}, {"propertyName": "2621"}, {"range": "2638", "text": "2503"}, {"propertyName": "2621"}, {"range": "2639", "text": "2503"}, {"propertyName": "2621"}, {"range": "2640", "text": "2503"}, {"propertyName": "2621"}, {"range": "2641", "text": "2503"}, {"propertyName": "2621"}, {"range": "2642", "text": "2503"}, {"propertyName": "2621"}, {"range": "2643", "text": "2503"}, {"propertyName": "2621"}, {"range": "2644", "text": "2503"}, {"propertyName": "2621"}, {"range": "2645", "text": "2503"}, {"propertyName": "2621"}, {"range": "2646", "text": "2503"}, {"propertyName": "2621"}, {"range": "2647", "text": "2503"}, {"propertyName": "2621"}, {"range": "2648", "text": "2503"}, {"propertyName": "2621"}, {"range": "2649", "text": "2503"}, {"propertyName": "2621"}, {"range": "2650", "text": "2503"}, {"propertyName": "2621"}, {"range": "2651", "text": "2503"}, {"propertyName": "2621"}, {"range": "2652", "text": "2503"}, {"propertyName": "2621"}, {"range": "2653", "text": "2503"}, {"propertyName": "2621"}, {"range": "2654", "text": "2503"}, {"propertyName": "2621"}, {"range": "2655", "text": "2503"}, {"propertyName": "2621"}, {"range": "2656", "text": "2503"}, {"propertyName": "2621"}, {"range": "2657", "text": "2503"}, {"propertyName": "2621"}, {"range": "2658", "text": "2503"}, {"propertyName": "2621"}, {"range": "2659", "text": "2503"}, {"propertyName": "2621"}, {"range": "2660", "text": "2503"}, {"propertyName": "2621"}, {"range": "2661", "text": "2503"}, {"propertyName": "2621"}, {"range": "2662", "text": "2503"}, {"propertyName": "2621"}, {"range": "2663", "text": "2503"}, {"propertyName": "2621"}, {"range": "2664", "text": "2503"}, {"propertyName": "2621"}, {"range": "2665", "text": "2503"}, {"propertyName": "2621"}, {"range": "2666", "text": "2503"}, {"propertyName": "2621"}, {"range": "2667", "text": "2503"}, "Update the dependencies array to be: [onTagC<PERSON>e, selectedPageCategory, selectedTags, tags]", {"range": "2668", "text": "2669"}, "Update the dependencies array to be: [defaultChip.id, onTagChange, selectedPageCategory, tags]", {"range": "2670", "text": "2671"}, "Update the dependencies array to be: [defaultChip.id, locale]", {"range": "2672", "text": "2673"}, {"propertyName": "2621"}, {"range": "2674", "text": "2503"}, "block", [2714, 2714], " /* empty */ ", [1145, 1159], "[on<PERSON><PERSON><PERSON>, selected<PERSON><PERSON>s]", [1576, 1586], "[filters, setFilters, tagIds, tagsData]", "log", [809, 827], [1251, 1365], [1797, 1815], [901, 943], [1103, 1142], [1233, 1273], [1382, 1448], [1312, 1391], [1617, 1709], [1518, 1597], [1823, 1915], [1319, 1366], [1578, 1652], [1890, 1972], [988, 1103], [1140, 1185], [1702, 1773], [2036, 2114], [739, 757], [897, 915], [1728, 1746], [1495, 1513], [117, 264], [2311, 2409], [2413, 2468], [2472, 2539], [2543, 2656], [3775, 3873], [3877, 3926], [3930, 3991], [3995, 4096], [4838, 4855], [4861, 4950], [4954, 5133], [5137, 5154], [6450, 6508], [6516, 6550], [6558, 6596], [6604, 6640], [6648, 6677], [6685, 6702], [6724, 6909], [6928, 6945], [6949, 7133], [7137, 7424], [7428, 7576], [2297, 2311], "[on<PERSON><PERSON><PERSON><PERSON><PERSON>, selectedPageCategory, selectedTags, tags]", [2683, 2705], "[defaultChip.id, onTagChange, selectedPageCategory, tags]", [2933, 2941], "[defaultChip.id, locale]", [1885, 1971]]