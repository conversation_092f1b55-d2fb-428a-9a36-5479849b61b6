{"kind": "collectionType", "collectionName": "document_categories", "info": {"singularName": "document-category", "pluralName": "document-categories", "displayName": "Dokumenty - kategórie", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "title", "required": true}, "documents": {"type": "relation", "relation": "oneToMany", "target": "api::document.document", "mappedBy": "documentCategory"}}}