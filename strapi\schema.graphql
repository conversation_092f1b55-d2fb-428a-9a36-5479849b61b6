### This file was generated by Nexus Schema
### Do not make changes to this file directly


type AdminGroup {
  adminGroupId: String
  articles(filters: ArticleFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): ArticleRelationResponseCollection
  createdAt: DateTime
  pages(filters: PageFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): PageRelationResponseCollection
  title: String
  updatedAt: DateTime
}

type AdminGroupEntity {
  attributes: AdminGroup
  id: ID
}

type AdminGroupEntityResponse {
  data: AdminGroupEntity
}

type AdminGroupEntityResponseCollection {
  data: [AdminGroupEntity!]!
  meta: ResponseCollectionMeta!
}

input AdminGroupFiltersInput {
  adminGroupId: StringFilterInput
  and: [AdminGroupFiltersInput]
  articles: ArticleFiltersInput
  createdAt: DateTimeFilterInput
  id: IDFilterInput
  not: AdminGroupFiltersInput
  or: [AdminGroupFiltersInput]
  pages: PageFiltersInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input AdminGroupInput {
  adminGroupId: String
  articles: [ID]
  pages: [ID]
  title: String
}

type AdminGroupRelationResponseCollection {
  data: [AdminGroupEntity!]!
}

type Alert {
  createdAt: DateTime
  locale: String
  localizations: AlertRelationResponseCollection
  text: String
  updatedAt: DateTime
}

type AlertEntity {
  attributes: Alert
  id: ID
}

type AlertEntityResponse {
  data: AlertEntity
}

type AlertEntityResponseCollection {
  data: [AlertEntity!]!
  meta: ResponseCollectionMeta!
}

input AlertFiltersInput {
  and: [AlertFiltersInput]
  createdAt: DateTimeFilterInput
  locale: StringFilterInput
  localizations: AlertFiltersInput
  not: AlertFiltersInput
  or: [AlertFiltersInput]
  text: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input AlertInput {
  text: String
}

type AlertRelationResponseCollection {
  data: [AlertEntity!]!
}

type Article {
  addedAt: DateTime!
  adminGroups(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): AdminGroupRelationResponseCollection
  alias: String
  content: String
  coverMedia: UploadFileEntityResponse
  createdAt: DateTime
  files(filters: ComponentBlocksFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksFile]
  gallery(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UploadFileRelationResponseCollection
  locale: String
  localizations(filters: ArticleFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): ArticleRelationResponseCollection
  perex: String
  publishedAt: DateTime
  slug: String!
  tag: TagEntityResponse
  title: String!
  updatedAt: DateTime
}

type ArticleEntity {
  attributes: Article
  id: ID
}

type ArticleEntityResponse {
  data: ArticleEntity
}

type ArticleEntityResponseCollection {
  data: [ArticleEntity!]!
  meta: ResponseCollectionMeta!
}

input ArticleFiltersInput {
  addedAt: DateTimeFilterInput
  adminGroups: AdminGroupFiltersInput
  alias: StringFilterInput
  and: [ArticleFiltersInput]
  content: StringFilterInput
  createdAt: DateTimeFilterInput
  files: ComponentBlocksFileFiltersInput
  id: IDFilterInput
  locale: StringFilterInput
  localizations: ArticleFiltersInput
  not: ArticleFiltersInput
  or: [ArticleFiltersInput]
  perex: StringFilterInput
  publishedAt: DateTimeFilterInput
  slug: StringFilterInput
  tag: TagFiltersInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input ArticleInput {
  addedAt: DateTime
  adminGroups: [ID]
  alias: String
  content: String
  coverMedia: ID
  files: [ComponentBlocksFileInput]
  gallery: [ID]
  perex: String
  publishedAt: DateTime
  slug: String
  tag: ID
  title: String
}

type ArticleRelationResponseCollection {
  data: [ArticleEntity!]!
}

type BlogPost {
  addedAt: DateTime!
  coverImage: UploadFileEntityResponse
  createdAt: DateTime
  date_added: DateTime
  excerpt: String
  locale: String
  localizations(filters: BlogPostFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): BlogPostRelationResponseCollection
  publishedAt: DateTime
  sections: [BlogPostSectionsDynamicZone]
  slug: String!
  tag: TagEntityResponse
  title: String!
  updatedAt: DateTime
}

type BlogPostEntity {
  attributes: BlogPost
  id: ID
}

type BlogPostEntityResponse {
  data: BlogPostEntity
}

type BlogPostEntityResponseCollection {
  data: [BlogPostEntity!]!
  meta: ResponseCollectionMeta!
}

input BlogPostFiltersInput {
  addedAt: DateTimeFilterInput
  and: [BlogPostFiltersInput]
  createdAt: DateTimeFilterInput
  date_added: DateTimeFilterInput
  excerpt: StringFilterInput
  id: IDFilterInput
  locale: StringFilterInput
  localizations: BlogPostFiltersInput
  not: BlogPostFiltersInput
  or: [BlogPostFiltersInput]
  publishedAt: DateTimeFilterInput
  slug: StringFilterInput
  tag: TagFiltersInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input BlogPostInput {
  addedAt: DateTime
  coverImage: ID
  date_added: DateTime
  excerpt: String
  publishedAt: DateTime
  sections: [BlogPostSectionsDynamicZoneInput!]
  slug: String
  tag: ID
  title: String
}

type BlogPostRelationResponseCollection {
  data: [BlogPostEntity!]!
}

union BlogPostSectionsDynamicZone = ComponentSectionsFileList | ComponentSectionsGallery | ComponentSectionsNarrowText | Error

scalar BlogPostSectionsDynamicZoneInput

input BooleanFilterInput {
  and: [Boolean]
  between: [Boolean]
  contains: Boolean
  containsi: Boolean
  endsWith: Boolean
  eq: Boolean
  eqi: Boolean
  gt: Boolean
  gte: Boolean
  in: [Boolean]
  lt: Boolean
  lte: Boolean
  ne: Boolean
  nei: Boolean
  not: BooleanFilterInput
  notContains: Boolean
  notContainsi: Boolean
  notIn: [Boolean]
  notNull: Boolean
  null: Boolean
  or: [Boolean]
  startsWith: Boolean
}

type ComponentAccordionItemsFlatText {
  align: ENUM_COMPONENTACCORDIONITEMSFLATTEXT_ALIGN
  category: String
  content: String
  fileList(filters: ComponentBlocksFileItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksFileItem]
  id: ID!
  moreLinkPage: PageEntityResponse
  moreLinkTitle: String
  moreLinkUrl: String
  width: ENUM_COMPONENTACCORDIONITEMSFLATTEXT_WIDTH
}

input ComponentAccordionItemsFlatTextFiltersInput {
  align: StringFilterInput
  and: [ComponentAccordionItemsFlatTextFiltersInput]
  category: StringFilterInput
  content: StringFilterInput
  fileList: ComponentBlocksFileItemFiltersInput
  moreLinkPage: PageFiltersInput
  moreLinkTitle: StringFilterInput
  moreLinkUrl: StringFilterInput
  not: ComponentAccordionItemsFlatTextFiltersInput
  or: [ComponentAccordionItemsFlatTextFiltersInput]
  width: StringFilterInput
}

input ComponentAccordionItemsFlatTextInput {
  align: ENUM_COMPONENTACCORDIONITEMSFLATTEXT_ALIGN
  category: String
  content: String
  fileList: [ComponentBlocksFileItemInput]
  id: ID
  moreLinkPage: ID
  moreLinkTitle: String
  moreLinkUrl: String
  width: ENUM_COMPONENTACCORDIONITEMSFLATTEXT_WIDTH
}

type ComponentAccordionItemsInstitution {
  category: String
  firstColumn: String
  id: ID!
  secondColumn: String
  subtitle: String
  thirdColumn: String
  title: String
  url: String
  urlLabel: String
}

input ComponentAccordionItemsInstitutionFiltersInput {
  and: [ComponentAccordionItemsInstitutionFiltersInput]
  category: StringFilterInput
  firstColumn: StringFilterInput
  not: ComponentAccordionItemsInstitutionFiltersInput
  or: [ComponentAccordionItemsInstitutionFiltersInput]
  secondColumn: StringFilterInput
  subtitle: StringFilterInput
  thirdColumn: StringFilterInput
  title: StringFilterInput
  url: StringFilterInput
  urlLabel: StringFilterInput
}

input ComponentAccordionItemsInstitutionInput {
  category: String
  firstColumn: String
  id: ID
  secondColumn: String
  subtitle: String
  thirdColumn: String
  title: String
  url: String
  urlLabel: String
}

type ComponentAccordionItemsInstitutionNarrow {
  category: String
  id: ID!
  subtitle: String
  title: String
  url: String
  urlLabel: String
}

input ComponentAccordionItemsInstitutionNarrowFiltersInput {
  and: [ComponentAccordionItemsInstitutionNarrowFiltersInput]
  category: StringFilterInput
  not: ComponentAccordionItemsInstitutionNarrowFiltersInput
  or: [ComponentAccordionItemsInstitutionNarrowFiltersInput]
  subtitle: StringFilterInput
  title: StringFilterInput
  url: StringFilterInput
  urlLabel: StringFilterInput
}

input ComponentAccordionItemsInstitutionNarrowInput {
  category: String
  id: ID
  subtitle: String
  title: String
  url: String
  urlLabel: String
}

type ComponentBlocksColumnsItem {
  id: ID!
  image: UploadFileEntityResponse
  text: String
  title: String
}

input ComponentBlocksColumnsItemFiltersInput {
  and: [ComponentBlocksColumnsItemFiltersInput]
  not: ComponentBlocksColumnsItemFiltersInput
  or: [ComponentBlocksColumnsItemFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentBlocksColumnsItemInput {
  id: ID
  image: ID
  text: String
  title: String
}

type ComponentBlocksCommonLink {
  analyticsId: String
  article: ArticleEntityResponse
  id: ID!
  label: String
  page: PageEntityResponse
  url: String
}

input ComponentBlocksCommonLinkFiltersInput {
  analyticsId: StringFilterInput
  and: [ComponentBlocksCommonLinkFiltersInput]
  article: ArticleFiltersInput
  label: StringFilterInput
  not: ComponentBlocksCommonLinkFiltersInput
  or: [ComponentBlocksCommonLinkFiltersInput]
  page: PageFiltersInput
  url: StringFilterInput
}

input ComponentBlocksCommonLinkInput {
  analyticsId: String
  article: ID
  id: ID
  label: String
  page: ID
  url: String
}

type ComponentBlocksComparisonCard {
  iconMedia: UploadFileEntityResponse
  id: ID!
  items(filters: ComponentBlocksComparisonItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksComparisonItem]!
  title: String!
}

input ComponentBlocksComparisonCardFiltersInput {
  and: [ComponentBlocksComparisonCardFiltersInput]
  items: ComponentBlocksComparisonItemFiltersInput
  not: ComponentBlocksComparisonCardFiltersInput
  or: [ComponentBlocksComparisonCardFiltersInput]
  title: StringFilterInput
}

input ComponentBlocksComparisonCardInput {
  iconMedia: ID
  id: ID
  items: [ComponentBlocksComparisonItemInput]
  title: String
}

type ComponentBlocksComparisonItem {
  id: ID!
  label: String!
}

input ComponentBlocksComparisonItemFiltersInput {
  and: [ComponentBlocksComparisonItemFiltersInput]
  label: StringFilterInput
  not: ComponentBlocksComparisonItemFiltersInput
  or: [ComponentBlocksComparisonItemFiltersInput]
}

input ComponentBlocksComparisonItemInput {
  id: ID
  label: String
}

type ComponentBlocksContactCard {
  id: ID!
  overrideLabel: String
  value: String!
}

input ComponentBlocksContactCardFiltersInput {
  and: [ComponentBlocksContactCardFiltersInput]
  not: ComponentBlocksContactCardFiltersInput
  or: [ComponentBlocksContactCardFiltersInput]
  overrideLabel: StringFilterInput
  value: StringFilterInput
}

input ComponentBlocksContactCardInput {
  id: ID
  overrideLabel: String
  value: String
}

type ComponentBlocksContactPersonCard {
  email: String
  id: ID!
  phone: String
  subtext: String
  title: String!
}

input ComponentBlocksContactPersonCardFiltersInput {
  and: [ComponentBlocksContactPersonCardFiltersInput]
  email: StringFilterInput
  not: ComponentBlocksContactPersonCardFiltersInput
  or: [ComponentBlocksContactPersonCardFiltersInput]
  phone: StringFilterInput
  subtext: StringFilterInput
  title: StringFilterInput
}

input ComponentBlocksContactPersonCardInput {
  email: String
  id: ID
  phone: String
  subtext: String
  title: String
}

type ComponentBlocksFile {
  id: ID!
  media: UploadFileEntityResponse
  title: String
}

input ComponentBlocksFileFiltersInput {
  and: [ComponentBlocksFileFiltersInput]
  not: ComponentBlocksFileFiltersInput
  or: [ComponentBlocksFileFiltersInput]
  title: StringFilterInput
}

input ComponentBlocksFileInput {
  id: ID
  media: ID
  title: String
}

type ComponentBlocksFileItem {
  id: ID!
  media: UploadFileEntityResponse!
  title: String
}

input ComponentBlocksFileItemFiltersInput {
  and: [ComponentBlocksFileItemFiltersInput]
  not: ComponentBlocksFileItemFiltersInput
  or: [ComponentBlocksFileItemFiltersInput]
  title: StringFilterInput
}

input ComponentBlocksFileItemInput {
  id: ID
  media: ID
  title: String
}

type ComponentBlocksFooterColumn {
  id: ID!
  links(filters: ComponentBlocksCommonLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksCommonLink]
  title: String!
}

input ComponentBlocksFooterColumnFiltersInput {
  and: [ComponentBlocksFooterColumnFiltersInput]
  links: ComponentBlocksCommonLinkFiltersInput
  not: ComponentBlocksFooterColumnFiltersInput
  or: [ComponentBlocksFooterColumnFiltersInput]
  title: StringFilterInput
}

input ComponentBlocksFooterColumnInput {
  id: ID
  links: [ComponentBlocksCommonLinkInput]
  title: String
}

type ComponentBlocksHomepageHighlightsItem {
  id: ID!
  image: UploadFileEntityResponse!
  link: ComponentBlocksCommonLink!
}

input ComponentBlocksHomepageHighlightsItemFiltersInput {
  and: [ComponentBlocksHomepageHighlightsItemFiltersInput]
  link: ComponentBlocksCommonLinkFiltersInput
  not: ComponentBlocksHomepageHighlightsItemFiltersInput
  or: [ComponentBlocksHomepageHighlightsItemFiltersInput]
}

input ComponentBlocksHomepageHighlightsItemInput {
  id: ID
  image: ID
  link: ComponentBlocksCommonLinkInput
}

type ComponentBlocksIconWithTitleAndDescription {
  desc: String
  disableIconBackground: Boolean
  icon: UploadFileEntityResponse
  id: ID!
  title: String
}

input ComponentBlocksIconWithTitleAndDescriptionFiltersInput {
  and: [ComponentBlocksIconWithTitleAndDescriptionFiltersInput]
  desc: StringFilterInput
  disableIconBackground: BooleanFilterInput
  not: ComponentBlocksIconWithTitleAndDescriptionFiltersInput
  or: [ComponentBlocksIconWithTitleAndDescriptionFiltersInput]
  title: StringFilterInput
}

input ComponentBlocksIconWithTitleAndDescriptionInput {
  desc: String
  disableIconBackground: Boolean
  icon: ID
  id: ID
  title: String
}

type ComponentBlocksInBa {
  content: String
  id: ID!
  title: String
}

input ComponentBlocksInBaFiltersInput {
  and: [ComponentBlocksInBaFiltersInput]
  content: StringFilterInput
  not: ComponentBlocksInBaFiltersInput
  or: [ComponentBlocksInBaFiltersInput]
  title: StringFilterInput
}

input ComponentBlocksInBaInput {
  content: String
  id: ID
  title: String
}

type ComponentBlocksNumericalListItem {
  id: ID!
  text: String
}

input ComponentBlocksNumericalListItemFiltersInput {
  and: [ComponentBlocksNumericalListItemFiltersInput]
  not: ComponentBlocksNumericalListItemFiltersInput
  or: [ComponentBlocksNumericalListItemFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentBlocksNumericalListItemInput {
  id: ID
  text: String
  title: String
}

type ComponentBlocksPageLink {
  analyticsId: String
  id: ID!
  page: PageEntityResponse
  title: String
  url: String
}

input ComponentBlocksPageLinkFiltersInput {
  analyticsId: StringFilterInput
  and: [ComponentBlocksPageLinkFiltersInput]
  not: ComponentBlocksPageLinkFiltersInput
  or: [ComponentBlocksPageLinkFiltersInput]
  page: PageFiltersInput
  title: StringFilterInput
  url: StringFilterInput
}

input ComponentBlocksPageLinkInput {
  analyticsId: String
  id: ID
  page: ID
  title: String
  url: String
}

type ComponentBlocksPartner {
  id: ID!
  logo: UploadFileEntityResponse!
  title: String!
  url: String
}

input ComponentBlocksPartnerFiltersInput {
  and: [ComponentBlocksPartnerFiltersInput]
  not: ComponentBlocksPartnerFiltersInput
  or: [ComponentBlocksPartnerFiltersInput]
  title: StringFilterInput
  url: StringFilterInput
}

input ComponentBlocksPartnerInput {
  id: ID
  logo: ID
  title: String
  url: String
}

type ComponentBlocksProsAndConsCard {
  id: ID!
  items(filters: ComponentBlocksComparisonItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksComparisonItem]!
  title: String!
}

input ComponentBlocksProsAndConsCardFiltersInput {
  and: [ComponentBlocksProsAndConsCardFiltersInput]
  items: ComponentBlocksComparisonItemFiltersInput
  not: ComponentBlocksProsAndConsCardFiltersInput
  or: [ComponentBlocksProsAndConsCardFiltersInput]
  title: StringFilterInput
}

input ComponentBlocksProsAndConsCardInput {
  id: ID
  items: [ComponentBlocksComparisonItemInput]
  title: String
}

type ComponentBlocksTimelineItem {
  content: String
  id: ID!
  title: String
}

input ComponentBlocksTimelineItemFiltersInput {
  and: [ComponentBlocksTimelineItemFiltersInput]
  content: StringFilterInput
  not: ComponentBlocksTimelineItemFiltersInput
  or: [ComponentBlocksTimelineItemFiltersInput]
  title: StringFilterInput
}

input ComponentBlocksTimelineItemInput {
  content: String
  id: ID
  title: String
}

type ComponentBlocksTopServicesItem {
  icon: ENUM_COMPONENTBLOCKSTOPSERVICESITEM_ICON!
  id: ID!
  link: ComponentBlocksCommonLink!
}

input ComponentBlocksTopServicesItemFiltersInput {
  and: [ComponentBlocksTopServicesItemFiltersInput]
  icon: StringFilterInput
  link: ComponentBlocksCommonLinkFiltersInput
  not: ComponentBlocksTopServicesItemFiltersInput
  or: [ComponentBlocksTopServicesItemFiltersInput]
}

input ComponentBlocksTopServicesItemInput {
  icon: ENUM_COMPONENTBLOCKSTOPSERVICESITEM_ICON
  id: ID
  link: ComponentBlocksCommonLinkInput
}

type ComponentBlocksVideo {
  id: ID!
  speaker: String
  title: String
  url: String
}

input ComponentBlocksVideoFiltersInput {
  and: [ComponentBlocksVideoFiltersInput]
  not: ComponentBlocksVideoFiltersInput
  or: [ComponentBlocksVideoFiltersInput]
  speaker: StringFilterInput
  title: StringFilterInput
  url: StringFilterInput
}

input ComponentBlocksVideoInput {
  id: ID
  speaker: String
  title: String
  url: String
}

type ComponentGeneralHeader {
  accountLink: ComponentBlocksCommonLink
  id: ID!
  links(filters: ComponentGeneralHeaderLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentGeneralHeaderLink]
}

input ComponentGeneralHeaderFiltersInput {
  accountLink: ComponentBlocksCommonLinkFiltersInput
  and: [ComponentGeneralHeaderFiltersInput]
  links: ComponentGeneralHeaderLinkFiltersInput
  not: ComponentGeneralHeaderFiltersInput
  or: [ComponentGeneralHeaderFiltersInput]
}

input ComponentGeneralHeaderInput {
  accountLink: ComponentBlocksCommonLinkInput
  id: ID
  links: [ComponentGeneralHeaderLinkInput]
}

type ComponentGeneralHeaderLink {
  analyticsId: String
  icon: ENUM_COMPONENTGENERALHEADERLINK_ICON!
  id: ID!
  label: String
  page: PageEntityResponse
  showOnDesktop: Boolean!
  showOnMobile: Boolean!
  url: String
}

input ComponentGeneralHeaderLinkFiltersInput {
  analyticsId: StringFilterInput
  and: [ComponentGeneralHeaderLinkFiltersInput]
  icon: StringFilterInput
  label: StringFilterInput
  not: ComponentGeneralHeaderLinkFiltersInput
  or: [ComponentGeneralHeaderLinkFiltersInput]
  page: PageFiltersInput
  showOnDesktop: BooleanFilterInput
  showOnMobile: BooleanFilterInput
  url: StringFilterInput
}

input ComponentGeneralHeaderLinkInput {
  analyticsId: String
  icon: ENUM_COMPONENTGENERALHEADERLINK_ICON
  id: ID
  label: String
  page: ID
  showOnDesktop: Boolean
  showOnMobile: Boolean
  url: String
}

type ComponentMenuMenuItem {
  icon: ENUM_COMPONENTMENUMENUITEM_ICON!
  id: ID!
  label: String!
  page: PageEntityResponse
  sections(filters: ComponentMenuMenuSectionFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentMenuMenuSection]
}

input ComponentMenuMenuItemFiltersInput {
  and: [ComponentMenuMenuItemFiltersInput]
  icon: StringFilterInput
  label: StringFilterInput
  not: ComponentMenuMenuItemFiltersInput
  or: [ComponentMenuMenuItemFiltersInput]
  page: PageFiltersInput
  sections: ComponentMenuMenuSectionFiltersInput
}

input ComponentMenuMenuItemInput {
  icon: ENUM_COMPONENTMENUMENUITEM_ICON
  id: ID
  label: String
  page: ID
  sections: [ComponentMenuMenuSectionInput]
}

type ComponentMenuMenuLink {
  analyticsId: String
  id: ID!
  label: String
  page: PageEntityResponse
  url: String
}

input ComponentMenuMenuLinkFiltersInput {
  analyticsId: StringFilterInput
  and: [ComponentMenuMenuLinkFiltersInput]
  label: StringFilterInput
  not: ComponentMenuMenuLinkFiltersInput
  or: [ComponentMenuMenuLinkFiltersInput]
  page: PageFiltersInput
  url: StringFilterInput
}

input ComponentMenuMenuLinkInput {
  analyticsId: String
  id: ID
  label: String
  page: ID
  url: String
}

type ComponentMenuMenuSection {
  icon: ENUM_COMPONENTMENUMENUSECTION_ICON!
  id: ID!
  label: String!
  links(filters: ComponentMenuMenuLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentMenuMenuLink]
  page: PageEntityResponse
}

input ComponentMenuMenuSectionFiltersInput {
  and: [ComponentMenuMenuSectionFiltersInput]
  icon: StringFilterInput
  label: StringFilterInput
  links: ComponentMenuMenuLinkFiltersInput
  not: ComponentMenuMenuSectionFiltersInput
  or: [ComponentMenuMenuSectionFiltersInput]
  page: PageFiltersInput
}

input ComponentMenuMenuSectionInput {
  icon: ENUM_COMPONENTMENUMENUSECTION_ICON
  id: ID
  label: String
  links: [ComponentMenuMenuLinkInput]
  page: ID
}

type ComponentSectionsAccordion {
  flatText(filters: ComponentAccordionItemsFlatTextFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentAccordionItemsFlatText]
  id: ID!
  institutions(filters: ComponentAccordionItemsInstitutionFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentAccordionItemsInstitution]
  institutionsNarrow(filters: ComponentAccordionItemsInstitutionNarrowFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentAccordionItemsInstitutionNarrow]
  title: String
}

input ComponentSectionsAccordionFiltersInput {
  and: [ComponentSectionsAccordionFiltersInput]
  flatText: ComponentAccordionItemsFlatTextFiltersInput
  institutions: ComponentAccordionItemsInstitutionFiltersInput
  institutionsNarrow: ComponentAccordionItemsInstitutionNarrowFiltersInput
  not: ComponentSectionsAccordionFiltersInput
  or: [ComponentSectionsAccordionFiltersInput]
  title: StringFilterInput
}

input ComponentSectionsAccordionInput {
  flatText: [ComponentAccordionItemsFlatTextInput]
  id: ID
  institutions: [ComponentAccordionItemsInstitutionInput]
  institutionsNarrow: [ComponentAccordionItemsInstitutionNarrowInput]
  title: String
}

type ComponentSectionsArticles {
  category: PageCategoryEntityResponse
  id: ID!
  showAll: Boolean
  text: String
  title: String
}

input ComponentSectionsArticlesFiltersInput {
  and: [ComponentSectionsArticlesFiltersInput]
  category: PageCategoryFiltersInput
  not: ComponentSectionsArticlesFiltersInput
  or: [ComponentSectionsArticlesFiltersInput]
  showAll: BooleanFilterInput
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsArticlesInput {
  category: ID
  id: ID
  showAll: Boolean
  text: String
  title: String
}

type ComponentSectionsBanner {
  content: String
  contentPosition: ENUM_COMPONENTSECTIONSBANNER_CONTENTPOSITION!
  id: ID!
  media: UploadFileEntityResponse!
  primaryLink: ComponentBlocksCommonLink
  secondaryLink: ComponentBlocksCommonLink
  tertiaryLink: ComponentBlocksCommonLink
  title: String!
  variant: ENUM_COMPONENTSECTIONSBANNER_VARIANT!
}

input ComponentSectionsBannerFiltersInput {
  and: [ComponentSectionsBannerFiltersInput]
  content: StringFilterInput
  contentPosition: StringFilterInput
  not: ComponentSectionsBannerFiltersInput
  or: [ComponentSectionsBannerFiltersInput]
  primaryLink: ComponentBlocksCommonLinkFiltersInput
  secondaryLink: ComponentBlocksCommonLinkFiltersInput
  tertiaryLink: ComponentBlocksCommonLinkFiltersInput
  title: StringFilterInput
  variant: StringFilterInput
}

input ComponentSectionsBannerInput {
  content: String
  contentPosition: ENUM_COMPONENTSECTIONSBANNER_CONTENTPOSITION
  id: ID
  media: ID
  primaryLink: ComponentBlocksCommonLinkInput
  secondaryLink: ComponentBlocksCommonLinkInput
  tertiaryLink: ComponentBlocksCommonLinkInput
  title: String
  variant: ENUM_COMPONENTSECTIONSBANNER_VARIANT
}

type ComponentSectionsCalculator {
  another_adult_value: Float
  child_value: Float
  id: ID!
  single_adult_value: Float
}

input ComponentSectionsCalculatorFiltersInput {
  and: [ComponentSectionsCalculatorFiltersInput]
  another_adult_value: FloatFilterInput
  child_value: FloatFilterInput
  not: ComponentSectionsCalculatorFiltersInput
  or: [ComponentSectionsCalculatorFiltersInput]
  single_adult_value: FloatFilterInput
}

input ComponentSectionsCalculatorInput {
  another_adult_value: Float
  child_value: Float
  id: ID
  single_adult_value: Float
}

type ComponentSectionsColumnedText {
  content: String
  id: ID!
  title: String
}

input ComponentSectionsColumnedTextFiltersInput {
  and: [ComponentSectionsColumnedTextFiltersInput]
  content: StringFilterInput
  not: ComponentSectionsColumnedTextFiltersInput
  or: [ComponentSectionsColumnedTextFiltersInput]
  title: StringFilterInput
}

input ComponentSectionsColumnedTextInput {
  content: String
  id: ID
  title: String
}

type ComponentSectionsColumns {
  columns(filters: ComponentBlocksColumnsItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksColumnsItem]!
  id: ID!
  imageVariant: ENUM_COMPONENTSECTIONSCOLUMNS_IMAGEVARIANT!
  responsiveLayout: ENUM_COMPONENTSECTIONSCOLUMNS_RESPONSIVELAYOUT!
  text: String
  title: String
}

input ComponentSectionsColumnsFiltersInput {
  and: [ComponentSectionsColumnsFiltersInput]
  columns: ComponentBlocksColumnsItemFiltersInput
  imageVariant: StringFilterInput
  not: ComponentSectionsColumnsFiltersInput
  or: [ComponentSectionsColumnsFiltersInput]
  responsiveLayout: StringFilterInput
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsColumnsInput {
  columns: [ComponentBlocksColumnsItemInput]
  id: ID
  imageVariant: ENUM_COMPONENTSECTIONSCOLUMNS_IMAGEVARIANT
  responsiveLayout: ENUM_COMPONENTSECTIONSCOLUMNS_RESPONSIVELAYOUT
  text: String
  title: String
}

type ComponentSectionsComparisonSection {
  cards(filters: ComponentBlocksComparisonCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksComparisonCard]!
  id: ID!
  text: String
  textAlign: ENUM_COMPONENTSECTIONSCOMPARISONSECTION_TEXTALIGN!
  title: String
}

input ComponentSectionsComparisonSectionFiltersInput {
  and: [ComponentSectionsComparisonSectionFiltersInput]
  cards: ComponentBlocksComparisonCardFiltersInput
  not: ComponentSectionsComparisonSectionFiltersInput
  or: [ComponentSectionsComparisonSectionFiltersInput]
  text: StringFilterInput
  textAlign: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsComparisonSectionInput {
  cards: [ComponentBlocksComparisonCardInput]
  id: ID
  text: String
  textAlign: ENUM_COMPONENTSECTIONSCOMPARISONSECTION_TEXTALIGN
  title: String
}

type ComponentSectionsContactsSection {
  addressContacts(filters: ComponentBlocksContactCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactCard]
  description: String
  emailContacts(filters: ComponentBlocksContactCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactCard]
  id: ID!
  openingHoursContacts(filters: ComponentBlocksContactCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactCard]
  personContacts(filters: ComponentBlocksContactPersonCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactPersonCard]
  phoneContacts(filters: ComponentBlocksContactCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactCard]
  title: String
  webContacts(filters: ComponentBlocksContactCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactCard]
}

input ComponentSectionsContactsSectionFiltersInput {
  addressContacts: ComponentBlocksContactCardFiltersInput
  and: [ComponentSectionsContactsSectionFiltersInput]
  description: StringFilterInput
  emailContacts: ComponentBlocksContactCardFiltersInput
  not: ComponentSectionsContactsSectionFiltersInput
  openingHoursContacts: ComponentBlocksContactCardFiltersInput
  or: [ComponentSectionsContactsSectionFiltersInput]
  personContacts: ComponentBlocksContactPersonCardFiltersInput
  phoneContacts: ComponentBlocksContactCardFiltersInput
  title: StringFilterInput
  webContacts: ComponentBlocksContactCardFiltersInput
}

input ComponentSectionsContactsSectionInput {
  addressContacts: [ComponentBlocksContactCardInput]
  description: String
  emailContacts: [ComponentBlocksContactCardInput]
  id: ID
  openingHoursContacts: [ComponentBlocksContactCardInput]
  personContacts: [ComponentBlocksContactPersonCardInput]
  phoneContacts: [ComponentBlocksContactCardInput]
  title: String
  webContacts: [ComponentBlocksContactCardInput]
}

type ComponentSectionsDivider {
  id: ID!
  style: ENUM_COMPONENTSECTIONSDIVIDER_STYLE
}

input ComponentSectionsDividerFiltersInput {
  and: [ComponentSectionsDividerFiltersInput]
  not: ComponentSectionsDividerFiltersInput
  or: [ComponentSectionsDividerFiltersInput]
  style: StringFilterInput
}

input ComponentSectionsDividerInput {
  id: ID
  style: ENUM_COMPONENTSECTIONSDIVIDER_STYLE
}

type ComponentSectionsDocuments {
  documents(filters: DocumentFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): DocumentRelationResponseCollection
  id: ID!
  text: String
  title: String
}

input ComponentSectionsDocumentsFiltersInput {
  and: [ComponentSectionsDocumentsFiltersInput]
  documents: DocumentFiltersInput
  not: ComponentSectionsDocumentsFiltersInput
  or: [ComponentSectionsDocumentsFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsDocumentsInput {
  documents: [ID]
  id: ID
  text: String
  title: String
}

type ComponentSectionsFaqCategories {
  faqCategories(filters: FaqCategoryFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): FaqCategoryRelationResponseCollection
  id: ID!
  text: String
  title: String
}

input ComponentSectionsFaqCategoriesFiltersInput {
  and: [ComponentSectionsFaqCategoriesFiltersInput]
  faqCategories: FaqCategoryFiltersInput
  not: ComponentSectionsFaqCategoriesFiltersInput
  or: [ComponentSectionsFaqCategoriesFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsFaqCategoriesInput {
  faqCategories: [ID]
  id: ID
  text: String
  title: String
}

type ComponentSectionsFaqs {
  faqs(filters: FaqFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): FaqRelationResponseCollection
  id: ID!
  text: String
  title: String
}

input ComponentSectionsFaqsFiltersInput {
  and: [ComponentSectionsFaqsFiltersInput]
  faqs: FaqFiltersInput
  not: ComponentSectionsFaqsFiltersInput
  or: [ComponentSectionsFaqsFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsFaqsInput {
  faqs: [ID]
  id: ID
  text: String
  title: String
}

type ComponentSectionsFileList {
  fileList(filters: ComponentBlocksFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksFile]
  id: ID!
  text: String
  title: String
}

input ComponentSectionsFileListFiltersInput {
  and: [ComponentSectionsFileListFiltersInput]
  fileList: ComponentBlocksFileFiltersInput
  not: ComponentSectionsFileListFiltersInput
  or: [ComponentSectionsFileListFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsFileListInput {
  fileList: [ComponentBlocksFileInput]
  id: ID
  text: String
  title: String
}

type ComponentSectionsGallery {
  id: ID!
  medias(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UploadFileRelationResponseCollection!
  text: String
  title: String
}

input ComponentSectionsGalleryFiltersInput {
  and: [ComponentSectionsGalleryFiltersInput]
  not: ComponentSectionsGalleryFiltersInput
  or: [ComponentSectionsGalleryFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsGalleryInput {
  id: ID
  medias: [ID]
  text: String
  title: String
}

type ComponentSectionsHomepageEvents {
  eventsPageLink: ComponentBlocksCommonLink
  id: ID!
  text: String
  title: String
}

input ComponentSectionsHomepageEventsFiltersInput {
  and: [ComponentSectionsHomepageEventsFiltersInput]
  eventsPageLink: ComponentBlocksCommonLinkFiltersInput
  not: ComponentSectionsHomepageEventsFiltersInput
  or: [ComponentSectionsHomepageEventsFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsHomepageEventsInput {
  eventsPageLink: ComponentBlocksCommonLinkInput
  id: ID
  text: String
  title: String
}

type ComponentSectionsHomepageHighlights {
  cards(filters: ComponentBlocksHomepageHighlightsItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksHomepageHighlightsItem]
  id: ID!
  text: String
  title: String
}

input ComponentSectionsHomepageHighlightsFiltersInput {
  and: [ComponentSectionsHomepageHighlightsFiltersInput]
  cards: ComponentBlocksHomepageHighlightsItemFiltersInput
  not: ComponentSectionsHomepageHighlightsFiltersInput
  or: [ComponentSectionsHomepageHighlightsFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsHomepageHighlightsInput {
  cards: [ComponentBlocksHomepageHighlightsItemInput]
  id: ID
  text: String
  title: String
}

type ComponentSectionsHomepageMayorAndCouncil {
  councilCard: ComponentBlocksCommonLink
  id: ID!
  mayorCard: ComponentBlocksCommonLink
  text: String
  title: String
}

input ComponentSectionsHomepageMayorAndCouncilFiltersInput {
  and: [ComponentSectionsHomepageMayorAndCouncilFiltersInput]
  councilCard: ComponentBlocksCommonLinkFiltersInput
  mayorCard: ComponentBlocksCommonLinkFiltersInput
  not: ComponentSectionsHomepageMayorAndCouncilFiltersInput
  or: [ComponentSectionsHomepageMayorAndCouncilFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsHomepageMayorAndCouncilInput {
  councilCard: ComponentBlocksCommonLinkInput
  id: ID
  mayorCard: ComponentBlocksCommonLinkInput
  text: String
  title: String
}

type ComponentSectionsHomepageTabs {
  id: ID!
  leftArticle: ArticleEntityResponse
  newsPageLink: ComponentBlocksCommonLink
  officialBoardPageLink: ComponentBlocksCommonLink
  rightArticle: ArticleEntityResponse
  roadClosuresPageLink: ComponentBlocksCommonLink
}

input ComponentSectionsHomepageTabsFiltersInput {
  and: [ComponentSectionsHomepageTabsFiltersInput]
  leftArticle: ArticleFiltersInput
  newsPageLink: ComponentBlocksCommonLinkFiltersInput
  not: ComponentSectionsHomepageTabsFiltersInput
  officialBoardPageLink: ComponentBlocksCommonLinkFiltersInput
  or: [ComponentSectionsHomepageTabsFiltersInput]
  rightArticle: ArticleFiltersInput
  roadClosuresPageLink: ComponentBlocksCommonLinkFiltersInput
}

input ComponentSectionsHomepageTabsInput {
  id: ID
  leftArticle: ID
  newsPageLink: ComponentBlocksCommonLinkInput
  officialBoardPageLink: ComponentBlocksCommonLinkInput
  rightArticle: ID
  roadClosuresPageLink: ComponentBlocksCommonLinkInput
}

type ComponentSectionsIconTitleDesc {
  id: ID!
  list(filters: ComponentBlocksIconWithTitleAndDescriptionFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksIconWithTitleAndDescription]
  title: String
}

input ComponentSectionsIconTitleDescFiltersInput {
  and: [ComponentSectionsIconTitleDescFiltersInput]
  list: ComponentBlocksIconWithTitleAndDescriptionFiltersInput
  not: ComponentSectionsIconTitleDescFiltersInput
  or: [ComponentSectionsIconTitleDescFiltersInput]
  title: StringFilterInput
}

input ComponentSectionsIconTitleDescInput {
  id: ID
  list: [ComponentBlocksIconWithTitleAndDescriptionInput]
  title: String
}

type ComponentSectionsIframe {
  allowFullscreen: Boolean!
  allowGeolocation: Boolean
  css: String
  fullHeight: Boolean!
  id: ID!
  iframeHeight: String!
  iframeWidth: ENUM_COMPONENTSECTIONSIFRAME_IFRAMEWIDTH!
  url: String!
}

input ComponentSectionsIframeFiltersInput {
  allowFullscreen: BooleanFilterInput
  allowGeolocation: BooleanFilterInput
  and: [ComponentSectionsIframeFiltersInput]
  css: StringFilterInput
  fullHeight: BooleanFilterInput
  iframeHeight: StringFilterInput
  iframeWidth: StringFilterInput
  not: ComponentSectionsIframeFiltersInput
  or: [ComponentSectionsIframeFiltersInput]
  url: StringFilterInput
}

input ComponentSectionsIframeInput {
  allowFullscreen: Boolean
  allowGeolocation: Boolean
  css: String
  fullHeight: Boolean
  id: ID
  iframeHeight: String
  iframeWidth: ENUM_COMPONENTSECTIONSIFRAME_IFRAMEWIDTH
  url: String
}

type ComponentSectionsInbaArticlesList {
  id: ID!
  text: String
  title: String
}

input ComponentSectionsInbaArticlesListFiltersInput {
  and: [ComponentSectionsInbaArticlesListFiltersInput]
  not: ComponentSectionsInbaArticlesListFiltersInput
  or: [ComponentSectionsInbaArticlesListFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsInbaArticlesListInput {
  id: ID
  text: String
  title: String
}

type ComponentSectionsInbaReleases {
  id: ID!
  text: String
  title: String
}

input ComponentSectionsInbaReleasesFiltersInput {
  and: [ComponentSectionsInbaReleasesFiltersInput]
  not: ComponentSectionsInbaReleasesFiltersInput
  or: [ComponentSectionsInbaReleasesFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsInbaReleasesInput {
  id: ID
  text: String
  title: String
}

type ComponentSectionsLinks {
  id: ID!
  pageLinks(filters: ComponentBlocksPageLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksPageLink]
  title: String
}

input ComponentSectionsLinksFiltersInput {
  and: [ComponentSectionsLinksFiltersInput]
  not: ComponentSectionsLinksFiltersInput
  or: [ComponentSectionsLinksFiltersInput]
  pageLinks: ComponentBlocksPageLinkFiltersInput
  title: StringFilterInput
}

input ComponentSectionsLinksInput {
  id: ID
  pageLinks: [ComponentBlocksPageLinkInput]
  title: String
}

type ComponentSectionsNarrowText {
  align: ENUM_COMPONENTSECTIONSNARROWTEXT_ALIGN
  content: String
  id: ID!
  width: ENUM_COMPONENTSECTIONSNARROWTEXT_WIDTH
}

input ComponentSectionsNarrowTextFiltersInput {
  align: StringFilterInput
  and: [ComponentSectionsNarrowTextFiltersInput]
  content: StringFilterInput
  not: ComponentSectionsNarrowTextFiltersInput
  or: [ComponentSectionsNarrowTextFiltersInput]
  width: StringFilterInput
}

input ComponentSectionsNarrowTextInput {
  align: ENUM_COMPONENTSECTIONSNARROWTEXT_ALIGN
  content: String
  id: ID
  width: ENUM_COMPONENTSECTIONSNARROWTEXT_WIDTH
}

type ComponentSectionsNumericalList {
  buttonLink: String
  buttonText: String
  id: ID!
  items(filters: ComponentBlocksNumericalListItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksNumericalListItem]
  title: String
  variant: ENUM_COMPONENTSECTIONSNUMERICALLIST_VARIANT
}

input ComponentSectionsNumericalListFiltersInput {
  and: [ComponentSectionsNumericalListFiltersInput]
  buttonLink: StringFilterInput
  buttonText: StringFilterInput
  items: ComponentBlocksNumericalListItemFiltersInput
  not: ComponentSectionsNumericalListFiltersInput
  or: [ComponentSectionsNumericalListFiltersInput]
  title: StringFilterInput
  variant: StringFilterInput
}

input ComponentSectionsNumericalListInput {
  buttonLink: String
  buttonText: String
  id: ID
  items: [ComponentBlocksNumericalListItemInput]
  title: String
  variant: ENUM_COMPONENTSECTIONSNUMERICALLIST_VARIANT
}

type ComponentSectionsOfficialBoard {
  id: ID!
}

input ComponentSectionsOfficialBoardFiltersInput {
  and: [ComponentSectionsOfficialBoardFiltersInput]
  not: ComponentSectionsOfficialBoardFiltersInput
  or: [ComponentSectionsOfficialBoardFiltersInput]
}

input ComponentSectionsOfficialBoardInput {
  id: ID
}

type ComponentSectionsOrganizationalStructure {
  id: ID!
  title: String
}

input ComponentSectionsOrganizationalStructureFiltersInput {
  and: [ComponentSectionsOrganizationalStructureFiltersInput]
  not: ComponentSectionsOrganizationalStructureFiltersInput
  or: [ComponentSectionsOrganizationalStructureFiltersInput]
  title: StringFilterInput
}

input ComponentSectionsOrganizationalStructureInput {
  id: ID
  title: String
}

type ComponentSectionsPartners {
  id: ID!
  logoRatio: ENUM_COMPONENTSECTIONSPARTNERS_LOGORATIO!
  partners(filters: ComponentBlocksPartnerFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksPartner]!
  text: String
  title: String
}

input ComponentSectionsPartnersFiltersInput {
  and: [ComponentSectionsPartnersFiltersInput]
  logoRatio: StringFilterInput
  not: ComponentSectionsPartnersFiltersInput
  or: [ComponentSectionsPartnersFiltersInput]
  partners: ComponentBlocksPartnerFiltersInput
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsPartnersInput {
  id: ID
  logoRatio: ENUM_COMPONENTSECTIONSPARTNERS_LOGORATIO
  partners: [ComponentBlocksPartnerInput]
  text: String
  title: String
}

type ComponentSectionsProsAndConsSection {
  cons: ComponentBlocksProsAndConsCard!
  id: ID!
  pros: ComponentBlocksProsAndConsCard!
  text: String
  textAlign: ENUM_COMPONENTSECTIONSPROSANDCONSSECTION_TEXTALIGN!
  title: String
}

input ComponentSectionsProsAndConsSectionFiltersInput {
  and: [ComponentSectionsProsAndConsSectionFiltersInput]
  cons: ComponentBlocksProsAndConsCardFiltersInput
  not: ComponentSectionsProsAndConsSectionFiltersInput
  or: [ComponentSectionsProsAndConsSectionFiltersInput]
  pros: ComponentBlocksProsAndConsCardFiltersInput
  text: StringFilterInput
  textAlign: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsProsAndConsSectionInput {
  cons: ComponentBlocksProsAndConsCardInput
  id: ID
  pros: ComponentBlocksProsAndConsCardInput
  text: String
  textAlign: ENUM_COMPONENTSECTIONSPROSANDCONSSECTION_TEXTALIGN
  title: String
}

type ComponentSectionsRegulations {
  id: ID!
  regulations(filters: RegulationFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): RegulationRelationResponseCollection
}

input ComponentSectionsRegulationsFiltersInput {
  and: [ComponentSectionsRegulationsFiltersInput]
  not: ComponentSectionsRegulationsFiltersInput
  or: [ComponentSectionsRegulationsFiltersInput]
  regulations: RegulationFiltersInput
}

input ComponentSectionsRegulationsInput {
  id: ID
  regulations: [ID]
}

type ComponentSectionsRegulationsList {
  id: ID!
}

input ComponentSectionsRegulationsListFiltersInput {
  and: [ComponentSectionsRegulationsListFiltersInput]
  not: ComponentSectionsRegulationsListFiltersInput
  or: [ComponentSectionsRegulationsListFiltersInput]
}

input ComponentSectionsRegulationsListInput {
  id: ID
}

type ComponentSectionsSubpageList {
  id: ID!
  subpageList(filters: ComponentBlocksPageLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksPageLink]
}

input ComponentSectionsSubpageListFiltersInput {
  and: [ComponentSectionsSubpageListFiltersInput]
  not: ComponentSectionsSubpageListFiltersInput
  or: [ComponentSectionsSubpageListFiltersInput]
  subpageList: ComponentBlocksPageLinkFiltersInput
}

input ComponentSectionsSubpageListInput {
  id: ID
  subpageList: [ComponentBlocksPageLinkInput]
}

type ComponentSectionsTextWithImage {
  content: String
  id: ID!
  imageAspectRatio: ENUM_COMPONENTSECTIONSTEXTWITHIMAGE_IMAGEASPECTRATIO
  imagePosition: ENUM_COMPONENTSECTIONSTEXTWITHIMAGE_IMAGEPOSITION!
  imageSrc: UploadFileEntityResponse!
  links(filters: ComponentBlocksCommonLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksCommonLink]
}

input ComponentSectionsTextWithImageFiltersInput {
  and: [ComponentSectionsTextWithImageFiltersInput]
  content: StringFilterInput
  imageAspectRatio: StringFilterInput
  imagePosition: StringFilterInput
  links: ComponentBlocksCommonLinkFiltersInput
  not: ComponentSectionsTextWithImageFiltersInput
  or: [ComponentSectionsTextWithImageFiltersInput]
}

input ComponentSectionsTextWithImageInput {
  content: String
  id: ID
  imageAspectRatio: ENUM_COMPONENTSECTIONSTEXTWITHIMAGE_IMAGEASPECTRATIO
  imagePosition: ENUM_COMPONENTSECTIONSTEXTWITHIMAGE_IMAGEPOSITION
  imageSrc: ID
  links: [ComponentBlocksCommonLinkInput]
}

type ComponentSectionsTextWithImageOverlapped {
  content: String
  id: ID!
  image: UploadFileEntityResponse!
  imagePosition: ENUM_COMPONENTSECTIONSTEXTWITHIMAGEOVERLAPPED_IMAGEPOSITION!
  links(filters: ComponentBlocksCommonLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksCommonLink]
}

input ComponentSectionsTextWithImageOverlappedFiltersInput {
  and: [ComponentSectionsTextWithImageOverlappedFiltersInput]
  content: StringFilterInput
  imagePosition: StringFilterInput
  links: ComponentBlocksCommonLinkFiltersInput
  not: ComponentSectionsTextWithImageOverlappedFiltersInput
  or: [ComponentSectionsTextWithImageOverlappedFiltersInput]
}

input ComponentSectionsTextWithImageOverlappedInput {
  content: String
  id: ID
  image: ID
  imagePosition: ENUM_COMPONENTSECTIONSTEXTWITHIMAGEOVERLAPPED_IMAGEPOSITION
  links: [ComponentBlocksCommonLinkInput]
}

type ComponentSectionsTimeline {
  id: ID!
  timelineItems(filters: ComponentBlocksTimelineItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksTimelineItem]
}

input ComponentSectionsTimelineFiltersInput {
  and: [ComponentSectionsTimelineFiltersInput]
  not: ComponentSectionsTimelineFiltersInput
  or: [ComponentSectionsTimelineFiltersInput]
  timelineItems: ComponentBlocksTimelineItemFiltersInput
}

input ComponentSectionsTimelineInput {
  id: ID
  timelineItems: [ComponentBlocksTimelineItemInput]
}

type ComponentSectionsTootootEvents {
  id: ID!
  showMoreLink: ComponentBlocksCommonLink
  text: String
  title: String
}

input ComponentSectionsTootootEventsFiltersInput {
  and: [ComponentSectionsTootootEventsFiltersInput]
  not: ComponentSectionsTootootEventsFiltersInput
  or: [ComponentSectionsTootootEventsFiltersInput]
  showMoreLink: ComponentBlocksCommonLinkFiltersInput
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsTootootEventsInput {
  id: ID
  showMoreLink: ComponentBlocksCommonLinkInput
  text: String
  title: String
}

type ComponentSectionsTopServices {
  id: ID!
  services(filters: ComponentBlocksTopServicesItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksTopServicesItem]!
  title: String!
}

input ComponentSectionsTopServicesFiltersInput {
  and: [ComponentSectionsTopServicesFiltersInput]
  not: ComponentSectionsTopServicesFiltersInput
  or: [ComponentSectionsTopServicesFiltersInput]
  services: ComponentBlocksTopServicesItemFiltersInput
  title: StringFilterInput
}

input ComponentSectionsTopServicesInput {
  id: ID
  services: [ComponentBlocksTopServicesItemInput]
  title: String
}

type ComponentSectionsVideos {
  id: ID!
  subtitle: String
  title: String
  videos(filters: ComponentBlocksVideoFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksVideo]
}

input ComponentSectionsVideosFiltersInput {
  and: [ComponentSectionsVideosFiltersInput]
  not: ComponentSectionsVideosFiltersInput
  or: [ComponentSectionsVideosFiltersInput]
  subtitle: StringFilterInput
  title: StringFilterInput
  videos: ComponentBlocksVideoFiltersInput
}

input ComponentSectionsVideosInput {
  id: ID
  subtitle: String
  title: String
  videos: [ComponentBlocksVideoInput]
}

type ComponentTaxAdministratorsTaxAdministrator {
  email: String!
  id: ID!
  name: String!
  officeNumber: String!
  phone: String!
  range: String!
}

input ComponentTaxAdministratorsTaxAdministratorFiltersInput {
  and: [ComponentTaxAdministratorsTaxAdministratorFiltersInput]
  email: StringFilterInput
  name: StringFilterInput
  not: ComponentTaxAdministratorsTaxAdministratorFiltersInput
  officeNumber: StringFilterInput
  or: [ComponentTaxAdministratorsTaxAdministratorFiltersInput]
  phone: StringFilterInput
  range: StringFilterInput
}

input ComponentTaxAdministratorsTaxAdministratorInput {
  email: String
  id: ID
  name: String
  officeNumber: String
  phone: String
  range: String
}

"""
A date string, such as 2007-12-03, compliant with the `full-date` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.
"""
scalar Date

input DateFilterInput {
  and: [Date]
  between: [Date]
  contains: Date
  containsi: Date
  endsWith: Date
  eq: Date
  eqi: Date
  gt: Date
  gte: Date
  in: [Date]
  lt: Date
  lte: Date
  ne: Date
  nei: Date
  not: DateFilterInput
  notContains: Date
  notContainsi: Date
  notIn: [Date]
  notNull: Boolean
  null: Boolean
  or: [Date]
  startsWith: Date
}

"""
A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.
"""
scalar DateTime

input DateTimeFilterInput {
  and: [DateTime]
  between: [DateTime]
  contains: DateTime
  containsi: DateTime
  endsWith: DateTime
  eq: DateTime
  eqi: DateTime
  gt: DateTime
  gte: DateTime
  in: [DateTime]
  lt: DateTime
  lte: DateTime
  ne: DateTime
  nei: DateTime
  not: DateTimeFilterInput
  notContains: DateTime
  notContainsi: DateTime
  notIn: [DateTime]
  notNull: Boolean
  null: Boolean
  or: [DateTime]
  startsWith: DateTime
}

type Document {
  createdAt: DateTime
  description: String
  documentCategory: DocumentCategoryEntityResponse
  files(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UploadFileRelationResponseCollection!
  publishedAt: DateTime
  slug: String!
  title: String!
  updatedAt: DateTime
}

type DocumentCategory {
  createdAt: DateTime
  documents(filters: DocumentFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): DocumentRelationResponseCollection
  slug: String!
  title: String!
  updatedAt: DateTime
}

type DocumentCategoryEntity {
  attributes: DocumentCategory
  id: ID
}

type DocumentCategoryEntityResponse {
  data: DocumentCategoryEntity
}

type DocumentCategoryEntityResponseCollection {
  data: [DocumentCategoryEntity!]!
  meta: ResponseCollectionMeta!
}

input DocumentCategoryFiltersInput {
  and: [DocumentCategoryFiltersInput]
  createdAt: DateTimeFilterInput
  documents: DocumentFiltersInput
  id: IDFilterInput
  not: DocumentCategoryFiltersInput
  or: [DocumentCategoryFiltersInput]
  slug: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input DocumentCategoryInput {
  documents: [ID]
  slug: String
  title: String
}

type DocumentCategoryRelationResponseCollection {
  data: [DocumentCategoryEntity!]!
}

type DocumentEntity {
  attributes: Document
  id: ID
}

type DocumentEntityResponse {
  data: DocumentEntity
}

type DocumentEntityResponseCollection {
  data: [DocumentEntity!]!
  meta: ResponseCollectionMeta!
}

input DocumentFiltersInput {
  and: [DocumentFiltersInput]
  createdAt: DateTimeFilterInput
  description: StringFilterInput
  documentCategory: DocumentCategoryFiltersInput
  id: IDFilterInput
  not: DocumentFiltersInput
  or: [DocumentFiltersInput]
  publishedAt: DateTimeFilterInput
  slug: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input DocumentInput {
  description: String
  documentCategory: ID
  files: [ID]
  publishedAt: DateTime
  slug: String
  title: String
}

type DocumentRelationResponseCollection {
  data: [DocumentEntity!]!
}

enum ENUM_COMPONENTACCORDIONITEMSFLATTEXT_ALIGN {
  center
  left
  right
}

enum ENUM_COMPONENTACCORDIONITEMSFLATTEXT_WIDTH {
  default
  full
  narrow
  wide
}

enum ENUM_COMPONENTBLOCKSTOPSERVICESITEM_ICON {
  bratislavske_konto
  dane_a_poplatky
  kampane_a_projekty
  nahlasenie_podnetov
  organizacna_struktura
  pracovne_prilezitosti
  prenajom_priestorov
  turistom_v_hlavnom_meste
  uradne_hodiny
  verejne_priestory
}

enum ENUM_COMPONENTGENERALHEADERLINK_ICON {
  esluzby
  kontakt
  som_turista
  ukraina
}

enum ENUM_COMPONENTMENUMENUITEM_ICON {
  doprava_mapy_02
  kultura_06
  mesto_01
  socialna_pomoc_04
  vzdelavanie_05
  zp_vystavba_03
}

enum ENUM_COMPONENTMENUMENUSECTION_ICON {
  aktivity_04
  byvanie_04
  covid_06
  cyklo_02
  dane_01
  dedicstvo_06
  deti_a_mladez_05
  doprava_02
  dotacie_05
  kalendar_06
  klima_03
  komunity_06
  koncepcia_06
  mapy_02
  mhd_02
  ocenovanie_05
  organizacie_06
  parkovanie_02
  partnerstva_01
  pomoc_04
  projekty_01
  rozvoj_mesta_03
  skolstvo_05
  sluzby_04
  sluzby_06
  sport_05
  sprava_a_udrzba_02
  sprava_mesta_01
  transparentne_mesto_01
  uzemny_plan_03
  verejne_osvetlenie_03
  vystavba_a_nehnutelnosti_03
  zariadenia_04
  zdielana_mobilita_02
  zelen_03
  zivotne_prostredie_03
}

enum ENUM_COMPONENTSECTIONSBANNER_CONTENTPOSITION {
  left
  right
}

enum ENUM_COMPONENTSECTIONSBANNER_VARIANT {
  color
  dark
  white_condensed
}

enum ENUM_COMPONENTSECTIONSCOLUMNS_IMAGEVARIANT {
  columnsSection_imageVariant_imageOriginalSize
  columnsSection_imageVariant_withCircleBackground
}

enum ENUM_COMPONENTSECTIONSCOLUMNS_RESPONSIVELAYOUT {
  columnsSection_responsiveLayout_oneColumn
  columnsSection_responsiveLayout_slider
}

enum ENUM_COMPONENTSECTIONSCOMPARISONSECTION_TEXTALIGN {
  center
  left
}

enum ENUM_COMPONENTSECTIONSDIVIDER_STYLE {
  bicykel_02_full_width
  budovy_04_full_width
  byvanie_04_full_width
  divadlo
  doprava_02_full_width
  hrad_01_full_width
  lod_02_full_width
  mesto_01_full_width
  park_04_full_width
  parkovanie_02_full_width
  skola
  stromy_03_full_width
  vystavba_03_full_width
  vzdelavanie
}

enum ENUM_COMPONENTSECTIONSIFRAME_IFRAMEWIDTH {
  container
  full
}

enum ENUM_COMPONENTSECTIONSNARROWTEXT_ALIGN {
  center
  left
  right
}

enum ENUM_COMPONENTSECTIONSNARROWTEXT_WIDTH {
  default
  full
  narrow
  wide
}

enum ENUM_COMPONENTSECTIONSNUMERICALLIST_VARIANT {
  basic
  combined
  roadmap
}

enum ENUM_COMPONENTSECTIONSPARTNERS_LOGORATIO {
  ratio_4_1
  ratio_4_3
}

enum ENUM_COMPONENTSECTIONSPROSANDCONSSECTION_TEXTALIGN {
  center
  left
}

enum ENUM_COMPONENTSECTIONSTEXTWITHIMAGEOVERLAPPED_IMAGEPOSITION {
  left
  left_shifted
  right
  right_shifted
}

enum ENUM_COMPONENTSECTIONSTEXTWITHIMAGE_IMAGEASPECTRATIO {
  ratio_1_1
  ratio_4_3
}

enum ENUM_COMPONENTSECTIONSTEXTWITHIMAGE_IMAGEPOSITION {
  left
  right
}

enum ENUM_PAGECATEGORY_COLOR {
  blue
  brown
  green
  purple
  red
  yellow
}

enum ENUM_PAGECATEGORY_ICON {
  doprava_mapy_02
  kultura_06
  mesto_01
  socialna_pomoc_04
  vzdelavanie_05
  zp_vystavba_03
}

enum ENUM_PAGE_PAGECOLOR {
  blue
  brown
  green
  purple
  red
  yellow
}

enum ENUM_REGULATION_CATEGORY {
  archiv
  daneAPoplatky
  hospodarenie
  ostatne
  pomenovanieUlic
  poriadokACistota
  socialnaPomocASkolstvo
  uzemnePlanovanie
}

type Error {
  code: String!
  message: String
}

type Faq {
  body: String
  createdAt: DateTime
  faqCategory: FaqCategoryEntityResponse
  locale: String
  localizations(filters: FaqFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): FaqRelationResponseCollection
  publishedAt: DateTime
  title: String!
  updatedAt: DateTime
}

type FaqCategory {
  createdAt: DateTime
  faqs(filters: FaqFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): FaqRelationResponseCollection
  locale: String
  localizations(filters: FaqCategoryFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): FaqCategoryRelationResponseCollection
  publishedAt: DateTime
  slug: String!
  title: String!
  updatedAt: DateTime
}

type FaqCategoryEntity {
  attributes: FaqCategory
  id: ID
}

type FaqCategoryEntityResponse {
  data: FaqCategoryEntity
}

type FaqCategoryEntityResponseCollection {
  data: [FaqCategoryEntity!]!
  meta: ResponseCollectionMeta!
}

input FaqCategoryFiltersInput {
  and: [FaqCategoryFiltersInput]
  createdAt: DateTimeFilterInput
  faqs: FaqFiltersInput
  id: IDFilterInput
  locale: StringFilterInput
  localizations: FaqCategoryFiltersInput
  not: FaqCategoryFiltersInput
  or: [FaqCategoryFiltersInput]
  publishedAt: DateTimeFilterInput
  slug: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input FaqCategoryInput {
  faqs: [ID]
  publishedAt: DateTime
  slug: String
  title: String
}

type FaqCategoryRelationResponseCollection {
  data: [FaqCategoryEntity!]!
}

type FaqEntity {
  attributes: Faq
  id: ID
}

type FaqEntityResponse {
  data: FaqEntity
}

type FaqEntityResponseCollection {
  data: [FaqEntity!]!
  meta: ResponseCollectionMeta!
}

input FaqFiltersInput {
  and: [FaqFiltersInput]
  body: StringFilterInput
  createdAt: DateTimeFilterInput
  faqCategory: FaqCategoryFiltersInput
  id: IDFilterInput
  locale: StringFilterInput
  localizations: FaqFiltersInput
  not: FaqFiltersInput
  or: [FaqFiltersInput]
  publishedAt: DateTimeFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input FaqInput {
  body: String
  faqCategory: ID
  publishedAt: DateTime
  title: String
}

type FaqRelationResponseCollection {
  data: [FaqEntity!]!
}

input FileInfoInput {
  alternativeText: String
  caption: String
  name: String
}

input FloatFilterInput {
  and: [Float]
  between: [Float]
  contains: Float
  containsi: Float
  endsWith: Float
  eq: Float
  eqi: Float
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nei: Float
  not: FloatFilterInput
  notContains: Float
  notContainsi: Float
  notIn: [Float]
  notNull: Boolean
  null: Boolean
  or: [Float]
  startsWith: Float
}

type Footer {
  accessibilityPageLink: ComponentBlocksCommonLink
  columns(filters: ComponentBlocksFooterColumnFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksFooterColumn]
  contactText: String
  createdAt: DateTime
  facebookUrl: String
  innovationsLink: ComponentBlocksCommonLink
  instagramUrl: String
  locale: String
  localizations(publicationState: PublicationState = LIVE): FooterRelationResponseCollection
  publishedAt: DateTime
  updatedAt: DateTime
}

type FooterEntity {
  attributes: Footer
  id: ID
}

type FooterEntityResponse {
  data: FooterEntity
}

type FooterEntityResponseCollection {
  data: [FooterEntity!]!
  meta: ResponseCollectionMeta!
}

input FooterFiltersInput {
  accessibilityPageLink: ComponentBlocksCommonLinkFiltersInput
  and: [FooterFiltersInput]
  columns: ComponentBlocksFooterColumnFiltersInput
  contactText: StringFilterInput
  createdAt: DateTimeFilterInput
  facebookUrl: StringFilterInput
  innovationsLink: ComponentBlocksCommonLinkFiltersInput
  instagramUrl: StringFilterInput
  locale: StringFilterInput
  localizations: FooterFiltersInput
  not: FooterFiltersInput
  or: [FooterFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
}

input FooterInput {
  accessibilityPageLink: ComponentBlocksCommonLinkInput
  columns: [ComponentBlocksFooterColumnInput]
  contactText: String
  facebookUrl: String
  innovationsLink: ComponentBlocksCommonLinkInput
  instagramUrl: String
  publishedAt: DateTime
}

type FooterRelationResponseCollection {
  data: [FooterEntity!]!
}

type General {
  createdAt: DateTime
  header: ComponentGeneralHeader
  inbaPage: PageEntityResponse
  inbaReleasesPage: PageEntityResponse
  locale: String
  localizations: GeneralRelationResponseCollection
  newsPage: PageEntityResponse
  officialBoardPage: PageEntityResponse
  privacyPolicyPage: PageEntityResponse
  updatedAt: DateTime
  vznPage: PageEntityResponse
}

type GeneralEntity {
  attributes: General
  id: ID
}

type GeneralEntityResponse {
  data: GeneralEntity
}

type GeneralEntityResponseCollection {
  data: [GeneralEntity!]!
  meta: ResponseCollectionMeta!
}

input GeneralFiltersInput {
  and: [GeneralFiltersInput]
  createdAt: DateTimeFilterInput
  header: ComponentGeneralHeaderFiltersInput
  inbaPage: PageFiltersInput
  inbaReleasesPage: PageFiltersInput
  locale: StringFilterInput
  localizations: GeneralFiltersInput
  newsPage: PageFiltersInput
  not: GeneralFiltersInput
  officialBoardPage: PageFiltersInput
  or: [GeneralFiltersInput]
  privacyPolicyPage: PageFiltersInput
  updatedAt: DateTimeFilterInput
  vznPage: PageFiltersInput
}

input GeneralInput {
  header: ComponentGeneralHeaderInput
  inbaPage: ID
  inbaReleasesPage: ID
  newsPage: ID
  officialBoardPage: ID
  privacyPolicyPage: ID
  vznPage: ID
}

type GeneralRelationResponseCollection {
  data: [GeneralEntity!]!
}

union GenericMorph = AdminGroup | Alert | Article | BlogPost | ComponentAccordionItemsFlatText | ComponentAccordionItemsInstitution | ComponentAccordionItemsInstitutionNarrow | ComponentBlocksColumnsItem | ComponentBlocksCommonLink | ComponentBlocksComparisonCard | ComponentBlocksComparisonItem | ComponentBlocksContactCard | ComponentBlocksContactPersonCard | ComponentBlocksFile | ComponentBlocksFileItem | ComponentBlocksFooterColumn | ComponentBlocksHomepageHighlightsItem | ComponentBlocksIconWithTitleAndDescription | ComponentBlocksInBa | ComponentBlocksNumericalListItem | ComponentBlocksPageLink | ComponentBlocksPartner | ComponentBlocksProsAndConsCard | ComponentBlocksTimelineItem | ComponentBlocksTopServicesItem | ComponentBlocksVideo | ComponentGeneralHeader | ComponentGeneralHeaderLink | ComponentMenuMenuItem | ComponentMenuMenuLink | ComponentMenuMenuSection | ComponentSectionsAccordion | ComponentSectionsArticles | ComponentSectionsBanner | ComponentSectionsCalculator | ComponentSectionsColumnedText | ComponentSectionsColumns | ComponentSectionsComparisonSection | ComponentSectionsContactsSection | ComponentSectionsDivider | ComponentSectionsDocuments | ComponentSectionsFaqCategories | ComponentSectionsFaqs | ComponentSectionsFileList | ComponentSectionsGallery | ComponentSectionsHomepageEvents | ComponentSectionsHomepageHighlights | ComponentSectionsHomepageMayorAndCouncil | ComponentSectionsHomepageTabs | ComponentSectionsIconTitleDesc | ComponentSectionsIframe | ComponentSectionsInbaArticlesList | ComponentSectionsInbaReleases | ComponentSectionsLinks | ComponentSectionsNarrowText | ComponentSectionsNumericalList | ComponentSectionsOfficialBoard | ComponentSectionsOrganizationalStructure | ComponentSectionsPartners | ComponentSectionsProsAndConsSection | ComponentSectionsRegulations | ComponentSectionsRegulationsList | ComponentSectionsSubpageList | ComponentSectionsTextWithImage | ComponentSectionsTextWithImageOverlapped | ComponentSectionsTimeline | ComponentSectionsTootootEvents | ComponentSectionsTopServices | ComponentSectionsVideos | ComponentTaxAdministratorsTaxAdministrator | Document | DocumentCategory | Faq | FaqCategory | Footer | General | Homepage | I18NLocale | InbaArticle | InbaRelease | InbaTag | Menu | Page | PageCategory | Regulation | Tag | TaxAdministratorsList | UploadFile | UploadFolder | UsersPermissionsPermission | UsersPermissionsRole | UsersPermissionsUser

type Homepage {
  createdAt: DateTime
  eventsSection: ComponentSectionsTootootEvents
  highlights: ComponentSectionsHomepageHighlights
  inba: ComponentBlocksInBa
  inbaFrontImage: UploadFileEntityResponse!
  inbaRearImage: UploadFileEntityResponse!
  inbaUrl: String
  locale: String
  localizations(publicationState: PublicationState = LIVE): HomepageRelationResponseCollection
  mayorAndCouncil: ComponentSectionsHomepageMayorAndCouncil
  metaDescription: String!
  metaTitle: String!
  publishedAt: DateTime
  tabs: ComponentSectionsHomepageTabs
  topServices: ComponentSectionsTopServices
  updatedAt: DateTime
  welcomeHeadline: String!
  welcomeMedia: UploadFileEntityResponse!
}

type HomepageEntity {
  attributes: Homepage
  id: ID
}

type HomepageEntityResponse {
  data: HomepageEntity
}

type HomepageEntityResponseCollection {
  data: [HomepageEntity!]!
  meta: ResponseCollectionMeta!
}

input HomepageFiltersInput {
  and: [HomepageFiltersInput]
  createdAt: DateTimeFilterInput
  eventsSection: ComponentSectionsTootootEventsFiltersInput
  highlights: ComponentSectionsHomepageHighlightsFiltersInput
  inba: ComponentBlocksInBaFiltersInput
  inbaUrl: StringFilterInput
  locale: StringFilterInput
  localizations: HomepageFiltersInput
  mayorAndCouncil: ComponentSectionsHomepageMayorAndCouncilFiltersInput
  metaDescription: StringFilterInput
  metaTitle: StringFilterInput
  not: HomepageFiltersInput
  or: [HomepageFiltersInput]
  publishedAt: DateTimeFilterInput
  tabs: ComponentSectionsHomepageTabsFiltersInput
  topServices: ComponentSectionsTopServicesFiltersInput
  updatedAt: DateTimeFilterInput
  welcomeHeadline: StringFilterInput
}

input HomepageInput {
  eventsSection: ComponentSectionsTootootEventsInput
  highlights: ComponentSectionsHomepageHighlightsInput
  inba: ComponentBlocksInBaInput
  inbaFrontImage: ID
  inbaRearImage: ID
  inbaUrl: String
  mayorAndCouncil: ComponentSectionsHomepageMayorAndCouncilInput
  metaDescription: String
  metaTitle: String
  publishedAt: DateTime
  tabs: ComponentSectionsHomepageTabsInput
  topServices: ComponentSectionsTopServicesInput
  welcomeHeadline: String
  welcomeMedia: ID
}

type HomepageRelationResponseCollection {
  data: [HomepageEntity!]!
}

type I18NLocale {
  code: String
  createdAt: DateTime
  name: String
  updatedAt: DateTime
}

"""A string used to identify an i18n locale"""
scalar I18NLocaleCode

type I18NLocaleEntity {
  attributes: I18NLocale
  id: ID
}

type I18NLocaleEntityResponse {
  data: I18NLocaleEntity
}

type I18NLocaleEntityResponseCollection {
  data: [I18NLocaleEntity!]!
  meta: ResponseCollectionMeta!
}

input I18NLocaleFiltersInput {
  and: [I18NLocaleFiltersInput]
  code: StringFilterInput
  createdAt: DateTimeFilterInput
  id: IDFilterInput
  name: StringFilterInput
  not: I18NLocaleFiltersInput
  or: [I18NLocaleFiltersInput]
  updatedAt: DateTimeFilterInput
}

input I18NLocaleInput {
  code: String
  name: String
}

type I18NLocaleRelationResponseCollection {
  data: [I18NLocaleEntity!]!
}

input IDFilterInput {
  and: [ID]
  between: [ID]
  contains: ID
  containsi: ID
  endsWith: ID
  eq: ID
  eqi: ID
  gt: ID
  gte: ID
  in: [ID]
  lt: ID
  lte: ID
  ne: ID
  nei: ID
  not: IDFilterInput
  notContains: ID
  notContainsi: ID
  notIn: [ID]
  notNull: Boolean
  null: Boolean
  or: [ID]
  startsWith: ID
}

type InbaArticle {
  content: String
  coverImage: UploadFileEntityResponse
  createdAt: DateTime
  inbaRelease: InbaReleaseEntityResponse
  inbaTag: InbaTagEntityResponse
  locale: String
  localizations(filters: InbaArticleFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): InbaArticleRelationResponseCollection
  perex: String
  publishedAt: DateTime
  slug: String!
  title: String!
  updatedAt: DateTime
}

type InbaArticleEntity {
  attributes: InbaArticle
  id: ID
}

type InbaArticleEntityResponse {
  data: InbaArticleEntity
}

type InbaArticleEntityResponseCollection {
  data: [InbaArticleEntity!]!
  meta: ResponseCollectionMeta!
}

input InbaArticleFiltersInput {
  and: [InbaArticleFiltersInput]
  content: StringFilterInput
  createdAt: DateTimeFilterInput
  id: IDFilterInput
  inbaRelease: InbaReleaseFiltersInput
  inbaTag: InbaTagFiltersInput
  locale: StringFilterInput
  localizations: InbaArticleFiltersInput
  not: InbaArticleFiltersInput
  or: [InbaArticleFiltersInput]
  perex: StringFilterInput
  publishedAt: DateTimeFilterInput
  slug: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input InbaArticleInput {
  content: String
  coverImage: ID
  inbaRelease: ID
  inbaTag: ID
  perex: String
  publishedAt: DateTime
  slug: String
  title: String
}

type InbaArticleRelationResponseCollection {
  data: [InbaArticleEntity!]!
}

type InbaRelease {
  coverImage: UploadFileEntityResponse
  createdAt: DateTime
  files(filters: ComponentBlocksFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksFile]
  inbaArticles(filters: InbaArticleFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): InbaArticleRelationResponseCollection
  perex: String
  publishedAt: DateTime
  rearImage: UploadFileEntityResponse
  releaseDate: Date!
  slug: String!
  title: String!
  updatedAt: DateTime
}

type InbaReleaseEntity {
  attributes: InbaRelease
  id: ID
}

type InbaReleaseEntityResponse {
  data: InbaReleaseEntity
}

type InbaReleaseEntityResponseCollection {
  data: [InbaReleaseEntity!]!
  meta: ResponseCollectionMeta!
}

input InbaReleaseFiltersInput {
  and: [InbaReleaseFiltersInput]
  createdAt: DateTimeFilterInput
  files: ComponentBlocksFileFiltersInput
  id: IDFilterInput
  inbaArticles: InbaArticleFiltersInput
  not: InbaReleaseFiltersInput
  or: [InbaReleaseFiltersInput]
  perex: StringFilterInput
  publishedAt: DateTimeFilterInput
  releaseDate: DateFilterInput
  slug: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input InbaReleaseInput {
  coverImage: ID
  files: [ComponentBlocksFileInput]
  inbaArticles: [ID]
  perex: String
  publishedAt: DateTime
  rearImage: ID
  releaseDate: Date
  slug: String
  title: String
}

type InbaReleaseRelationResponseCollection {
  data: [InbaReleaseEntity!]!
}

type InbaTag {
  createdAt: DateTime
  inbaArticles(filters: InbaArticleFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): InbaArticleRelationResponseCollection
  locale: String
  localizations(filters: InbaTagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): InbaTagRelationResponseCollection
  title: String!
  updatedAt: DateTime
}

type InbaTagEntity {
  attributes: InbaTag
  id: ID
}

type InbaTagEntityResponse {
  data: InbaTagEntity
}

type InbaTagEntityResponseCollection {
  data: [InbaTagEntity!]!
  meta: ResponseCollectionMeta!
}

input InbaTagFiltersInput {
  and: [InbaTagFiltersInput]
  createdAt: DateTimeFilterInput
  id: IDFilterInput
  inbaArticles: InbaArticleFiltersInput
  locale: StringFilterInput
  localizations: InbaTagFiltersInput
  not: InbaTagFiltersInput
  or: [InbaTagFiltersInput]
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input InbaTagInput {
  inbaArticles: [ID]
  title: String
}

type InbaTagRelationResponseCollection {
  data: [InbaTagEntity!]!
}

input IntFilterInput {
  and: [Int]
  between: [Int]
  contains: Int
  containsi: Int
  endsWith: Int
  eq: Int
  eqi: Int
  gt: Int
  gte: Int
  in: [Int]
  lt: Int
  lte: Int
  ne: Int
  nei: Int
  not: IntFilterInput
  notContains: Int
  notContainsi: Int
  notIn: [Int]
  notNull: Boolean
  null: Boolean
  or: [Int]
  startsWith: Int
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

input JSONFilterInput {
  and: [JSON]
  between: [JSON]
  contains: JSON
  containsi: JSON
  endsWith: JSON
  eq: JSON
  eqi: JSON
  gt: JSON
  gte: JSON
  in: [JSON]
  lt: JSON
  lte: JSON
  ne: JSON
  nei: JSON
  not: JSONFilterInput
  notContains: JSON
  notContainsi: JSON
  notIn: [JSON]
  notNull: Boolean
  null: Boolean
  or: [JSON]
  startsWith: JSON
}

"""
The `BigInt` scalar type represents non-fractional signed whole numeric values.
"""
scalar Long

input LongFilterInput {
  and: [Long]
  between: [Long]
  contains: Long
  containsi: Long
  endsWith: Long
  eq: Long
  eqi: Long
  gt: Long
  gte: Long
  in: [Long]
  lt: Long
  lte: Long
  ne: Long
  nei: Long
  not: LongFilterInput
  notContains: Long
  notContainsi: Long
  notIn: [Long]
  notNull: Boolean
  null: Boolean
  or: [Long]
  startsWith: Long
}

type Menu {
  createdAt: DateTime
  locale: String
  localizations: MenuRelationResponseCollection
  menus(filters: ComponentMenuMenuItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentMenuMenuItem]
  updatedAt: DateTime
}

type MenuEntity {
  attributes: Menu
  id: ID
}

type MenuEntityResponse {
  data: MenuEntity
}

type MenuEntityResponseCollection {
  data: [MenuEntity!]!
  meta: ResponseCollectionMeta!
}

input MenuFiltersInput {
  and: [MenuFiltersInput]
  createdAt: DateTimeFilterInput
  locale: StringFilterInput
  localizations: MenuFiltersInput
  menus: ComponentMenuMenuItemFiltersInput
  not: MenuFiltersInput
  or: [MenuFiltersInput]
  updatedAt: DateTimeFilterInput
}

input MenuInput {
  menus: [ComponentMenuMenuItemInput]
}

type MenuRelationResponseCollection {
  data: [MenuEntity!]!
}

type Mutation {
  """Change user password. Confirm with the current password."""
  changePassword(currentPassword: String!, password: String!, passwordConfirmation: String!): UsersPermissionsLoginPayload
  createAdminGroup(data: AdminGroupInput!): AdminGroupEntityResponse
  createAlertLocalization(data: AlertInput, id: ID, locale: I18NLocaleCode): AlertEntityResponse
  createArticle(data: ArticleInput!, locale: I18NLocaleCode): ArticleEntityResponse
  createArticleLocalization(data: ArticleInput, id: ID, locale: I18NLocaleCode): ArticleEntityResponse
  createBlogPost(data: BlogPostInput!, locale: I18NLocaleCode): BlogPostEntityResponse
  createBlogPostLocalization(data: BlogPostInput, id: ID, locale: I18NLocaleCode): BlogPostEntityResponse
  createDocument(data: DocumentInput!): DocumentEntityResponse
  createDocumentCategory(data: DocumentCategoryInput!): DocumentCategoryEntityResponse
  createFaq(data: FaqInput!, locale: I18NLocaleCode): FaqEntityResponse
  createFaqCategory(data: FaqCategoryInput!, locale: I18NLocaleCode): FaqCategoryEntityResponse
  createFaqCategoryLocalization(data: FaqCategoryInput, id: ID, locale: I18NLocaleCode): FaqCategoryEntityResponse
  createFaqLocalization(data: FaqInput, id: ID, locale: I18NLocaleCode): FaqEntityResponse
  createFooterLocalization(data: FooterInput, id: ID, locale: I18NLocaleCode): FooterEntityResponse
  createGeneralLocalization(data: GeneralInput, id: ID, locale: I18NLocaleCode): GeneralEntityResponse
  createHomepageLocalization(data: HomepageInput, id: ID, locale: I18NLocaleCode): HomepageEntityResponse
  createInbaArticle(data: InbaArticleInput!, locale: I18NLocaleCode): InbaArticleEntityResponse
  createInbaArticleLocalization(data: InbaArticleInput, id: ID, locale: I18NLocaleCode): InbaArticleEntityResponse
  createInbaRelease(data: InbaReleaseInput!): InbaReleaseEntityResponse
  createInbaTag(data: InbaTagInput!, locale: I18NLocaleCode): InbaTagEntityResponse
  createInbaTagLocalization(data: InbaTagInput, id: ID, locale: I18NLocaleCode): InbaTagEntityResponse
  createMenuLocalization(data: MenuInput, id: ID, locale: I18NLocaleCode): MenuEntityResponse
  createPage(data: PageInput!, locale: I18NLocaleCode): PageEntityResponse
  createPageCategory(data: PageCategoryInput!, locale: I18NLocaleCode): PageCategoryEntityResponse
  createPageCategoryLocalization(data: PageCategoryInput, id: ID, locale: I18NLocaleCode): PageCategoryEntityResponse
  createPageLocalization(data: PageInput, id: ID, locale: I18NLocaleCode): PageEntityResponse
  createRegulation(data: RegulationInput!): RegulationEntityResponse
  createTag(data: TagInput!, locale: I18NLocaleCode): TagEntityResponse
  createTagLocalization(data: TagInput, id: ID, locale: I18NLocaleCode): TagEntityResponse
  createUploadFile(data: UploadFileInput!): UploadFileEntityResponse
  createUploadFolder(data: UploadFolderInput!): UploadFolderEntityResponse

  """Create a new role"""
  createUsersPermissionsRole(data: UsersPermissionsRoleInput!): UsersPermissionsCreateRolePayload

  """Create a new user"""
  createUsersPermissionsUser(data: UsersPermissionsUserInput!): UsersPermissionsUserEntityResponse!
  deleteAdminGroup(id: ID!): AdminGroupEntityResponse
  deleteAlert(locale: I18NLocaleCode): AlertEntityResponse
  deleteArticle(id: ID!, locale: I18NLocaleCode): ArticleEntityResponse
  deleteBlogPost(id: ID!, locale: I18NLocaleCode): BlogPostEntityResponse
  deleteDocument(id: ID!): DocumentEntityResponse
  deleteDocumentCategory(id: ID!): DocumentCategoryEntityResponse
  deleteFaq(id: ID!, locale: I18NLocaleCode): FaqEntityResponse
  deleteFaqCategory(id: ID!, locale: I18NLocaleCode): FaqCategoryEntityResponse
  deleteFooter(locale: I18NLocaleCode): FooterEntityResponse
  deleteGeneral(locale: I18NLocaleCode): GeneralEntityResponse
  deleteHomepage(locale: I18NLocaleCode): HomepageEntityResponse
  deleteInbaArticle(id: ID!, locale: I18NLocaleCode): InbaArticleEntityResponse
  deleteInbaRelease(id: ID!): InbaReleaseEntityResponse
  deleteInbaTag(id: ID!, locale: I18NLocaleCode): InbaTagEntityResponse
  deleteMenu(locale: I18NLocaleCode): MenuEntityResponse
  deletePage(id: ID!, locale: I18NLocaleCode): PageEntityResponse
  deletePageCategory(id: ID!, locale: I18NLocaleCode): PageCategoryEntityResponse
  deleteRegulation(id: ID!): RegulationEntityResponse
  deleteTag(id: ID!, locale: I18NLocaleCode): TagEntityResponse
  deleteTaxAdministratorsList: TaxAdministratorsListEntityResponse
  deleteUploadFile(id: ID!): UploadFileEntityResponse
  deleteUploadFolder(id: ID!): UploadFolderEntityResponse

  """Delete an existing role"""
  deleteUsersPermissionsRole(id: ID!): UsersPermissionsDeleteRolePayload

  """Delete an existing user"""
  deleteUsersPermissionsUser(id: ID!): UsersPermissionsUserEntityResponse!

  """Confirm an email users email address"""
  emailConfirmation(confirmation: String!): UsersPermissionsLoginPayload

  """Request a reset password token"""
  forgotPassword(email: String!): UsersPermissionsPasswordPayload
  login(input: UsersPermissionsLoginInput!): UsersPermissionsLoginPayload!
  multipleUpload(field: String, files: [Upload]!, ref: String, refId: ID): [UploadFileEntityResponse]!

  """Register a user"""
  register(input: UsersPermissionsRegisterInput!): UsersPermissionsLoginPayload!
  removeFile(id: ID!): UploadFileEntityResponse

  """
  Reset user password. Confirm with a code (resetToken from forgotPassword)
  """
  resetPassword(code: String!, password: String!, passwordConfirmation: String!): UsersPermissionsLoginPayload
  updateAdminGroup(data: AdminGroupInput!, id: ID!): AdminGroupEntityResponse
  updateAlert(data: AlertInput!, locale: I18NLocaleCode): AlertEntityResponse
  updateArticle(data: ArticleInput!, id: ID!, locale: I18NLocaleCode): ArticleEntityResponse
  updateBlogPost(data: BlogPostInput!, id: ID!, locale: I18NLocaleCode): BlogPostEntityResponse
  updateDocument(data: DocumentInput!, id: ID!): DocumentEntityResponse
  updateDocumentCategory(data: DocumentCategoryInput!, id: ID!): DocumentCategoryEntityResponse
  updateFaq(data: FaqInput!, id: ID!, locale: I18NLocaleCode): FaqEntityResponse
  updateFaqCategory(data: FaqCategoryInput!, id: ID!, locale: I18NLocaleCode): FaqCategoryEntityResponse
  updateFileInfo(id: ID!, info: FileInfoInput): UploadFileEntityResponse!
  updateFooter(data: FooterInput!, locale: I18NLocaleCode): FooterEntityResponse
  updateGeneral(data: GeneralInput!, locale: I18NLocaleCode): GeneralEntityResponse
  updateHomepage(data: HomepageInput!, locale: I18NLocaleCode): HomepageEntityResponse
  updateInbaArticle(data: InbaArticleInput!, id: ID!, locale: I18NLocaleCode): InbaArticleEntityResponse
  updateInbaRelease(data: InbaReleaseInput!, id: ID!): InbaReleaseEntityResponse
  updateInbaTag(data: InbaTagInput!, id: ID!, locale: I18NLocaleCode): InbaTagEntityResponse
  updateMenu(data: MenuInput!, locale: I18NLocaleCode): MenuEntityResponse
  updatePage(data: PageInput!, id: ID!, locale: I18NLocaleCode): PageEntityResponse
  updatePageCategory(data: PageCategoryInput!, id: ID!, locale: I18NLocaleCode): PageCategoryEntityResponse
  updateRegulation(data: RegulationInput!, id: ID!): RegulationEntityResponse
  updateTag(data: TagInput!, id: ID!, locale: I18NLocaleCode): TagEntityResponse
  updateTaxAdministratorsList(data: TaxAdministratorsListInput!): TaxAdministratorsListEntityResponse
  updateUploadFile(data: UploadFileInput!, id: ID!): UploadFileEntityResponse
  updateUploadFolder(data: UploadFolderInput!, id: ID!): UploadFolderEntityResponse

  """Update an existing role"""
  updateUsersPermissionsRole(data: UsersPermissionsRoleInput!, id: ID!): UsersPermissionsUpdateRolePayload

  """Update an existing user"""
  updateUsersPermissionsUser(data: UsersPermissionsUserInput!, id: ID!): UsersPermissionsUserEntityResponse!
  upload(field: String, file: Upload!, info: FileInfoInput, ref: String, refId: ID): UploadFileEntityResponse!
}

type Page {
  adminGroups(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): AdminGroupRelationResponseCollection
  alias: String
  childPages(filters: PageFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): PageRelationResponseCollection
  createdAt: DateTime
  headerLinks(filters: ComponentBlocksCommonLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksCommonLink]
  keywords: String
  locale: String
  localizations(filters: PageFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): PageRelationResponseCollection
  metaDiscription: String
  pageBackgroundImage: UploadFileEntityResponse
  pageCategory: PageCategoryEntityResponse
  pageColor: ENUM_PAGE_PAGECOLOR
  pageHeaderSections: [PagePageHeaderSectionsDynamicZone]
  parentPage: PageEntityResponse
  publishedAt: DateTime
  relatedContents(filters: TagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): TagRelationResponseCollection
  sections: [PageSectionsDynamicZone]
  slug: String
  subtext: String
  title: String!
  updatedAt: DateTime
}

type PageCategory {
  color: ENUM_PAGECATEGORY_COLOR
  createdAt: DateTime
  icon: ENUM_PAGECATEGORY_ICON
  locale: String
  localizations(filters: PageCategoryFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): PageCategoryRelationResponseCollection
  pages(filters: PageFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): PageRelationResponseCollection
  publishedAt: DateTime
  shortTitle: String
  title: String
  updatedAt: DateTime
}

type PageCategoryEntity {
  attributes: PageCategory
  id: ID
}

type PageCategoryEntityResponse {
  data: PageCategoryEntity
}

type PageCategoryEntityResponseCollection {
  data: [PageCategoryEntity!]!
  meta: ResponseCollectionMeta!
}

input PageCategoryFiltersInput {
  and: [PageCategoryFiltersInput]
  color: StringFilterInput
  createdAt: DateTimeFilterInput
  icon: StringFilterInput
  id: IDFilterInput
  locale: StringFilterInput
  localizations: PageCategoryFiltersInput
  not: PageCategoryFiltersInput
  or: [PageCategoryFiltersInput]
  pages: PageFiltersInput
  publishedAt: DateTimeFilterInput
  shortTitle: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input PageCategoryInput {
  color: ENUM_PAGECATEGORY_COLOR
  icon: ENUM_PAGECATEGORY_ICON
  pages: [ID]
  publishedAt: DateTime
  shortTitle: String
  title: String
}

type PageCategoryRelationResponseCollection {
  data: [PageCategoryEntity!]!
}

type PageEntity {
  attributes: Page
  id: ID
}

type PageEntityResponse {
  data: PageEntity
}

type PageEntityResponseCollection {
  data: [PageEntity!]!
  meta: ResponseCollectionMeta!
}

input PageFiltersInput {
  adminGroups: AdminGroupFiltersInput
  alias: StringFilterInput
  and: [PageFiltersInput]
  childPages: PageFiltersInput
  createdAt: DateTimeFilterInput
  headerLinks: ComponentBlocksCommonLinkFiltersInput
  id: IDFilterInput
  keywords: StringFilterInput
  locale: StringFilterInput
  localizations: PageFiltersInput
  metaDiscription: StringFilterInput
  not: PageFiltersInput
  or: [PageFiltersInput]
  pageCategory: PageCategoryFiltersInput
  pageColor: StringFilterInput
  parentPage: PageFiltersInput
  publishedAt: DateTimeFilterInput
  relatedContents: TagFiltersInput
  slug: StringFilterInput
  subtext: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input PageInput {
  adminGroups: [ID]
  alias: String
  childPages: [ID]
  headerLinks: [ComponentBlocksCommonLinkInput]
  keywords: String
  metaDiscription: String
  pageBackgroundImage: ID
  pageCategory: ID
  pageColor: ENUM_PAGE_PAGECOLOR
  pageHeaderSections: [PagePageHeaderSectionsDynamicZoneInput!]
  parentPage: ID
  publishedAt: DateTime
  relatedContents: [ID]
  sections: [PageSectionsDynamicZoneInput!]
  slug: String
  subtext: String
  title: String
}

union PagePageHeaderSectionsDynamicZone = ComponentSectionsSubpageList | Error

scalar PagePageHeaderSectionsDynamicZoneInput

type PageRelationResponseCollection {
  data: [PageEntity!]!
}

union PageSectionsDynamicZone = ComponentSectionsAccordion | ComponentSectionsArticles | ComponentSectionsBanner | ComponentSectionsCalculator | ComponentSectionsColumnedText | ComponentSectionsColumns | ComponentSectionsComparisonSection | ComponentSectionsContactsSection | ComponentSectionsDivider | ComponentSectionsDocuments | ComponentSectionsFaqCategories | ComponentSectionsFaqs | ComponentSectionsFileList | ComponentSectionsGallery | ComponentSectionsIconTitleDesc | ComponentSectionsIframe | ComponentSectionsInbaArticlesList | ComponentSectionsInbaReleases | ComponentSectionsLinks | ComponentSectionsNarrowText | ComponentSectionsNumericalList | ComponentSectionsOfficialBoard | ComponentSectionsOrganizationalStructure | ComponentSectionsPartners | ComponentSectionsProsAndConsSection | ComponentSectionsRegulations | ComponentSectionsRegulationsList | ComponentSectionsTextWithImage | ComponentSectionsTextWithImageOverlapped | ComponentSectionsTimeline | ComponentSectionsTootootEvents | ComponentSectionsVideos | Error

scalar PageSectionsDynamicZoneInput

type Pagination {
  page: Int!
  pageCount: Int!
  pageSize: Int!
  total: Int!
}

input PaginationArg {
  limit: Int
  page: Int
  pageSize: Int
  start: Int
}

enum PublicationState {
  LIVE
  PREVIEW
}

type Query {
  adminGroup(id: ID): AdminGroupEntityResponse
  adminGroups(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): AdminGroupEntityResponseCollection
  alert(locale: I18NLocaleCode): AlertEntityResponse
  article(id: ID, locale: I18NLocaleCode): ArticleEntityResponse
  articles(filters: ArticleFiltersInput, locale: I18NLocaleCode, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): ArticleEntityResponseCollection
  blogPost(id: ID, locale: I18NLocaleCode): BlogPostEntityResponse
  blogPosts(filters: BlogPostFiltersInput, locale: I18NLocaleCode, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): BlogPostEntityResponseCollection
  document(id: ID): DocumentEntityResponse
  documentCategories(filters: DocumentCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): DocumentCategoryEntityResponseCollection
  documentCategory(id: ID): DocumentCategoryEntityResponse
  documents(filters: DocumentFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): DocumentEntityResponseCollection
  faq(id: ID, locale: I18NLocaleCode): FaqEntityResponse
  faqCategories(filters: FaqCategoryFiltersInput, locale: I18NLocaleCode, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): FaqCategoryEntityResponseCollection
  faqCategory(id: ID, locale: I18NLocaleCode): FaqCategoryEntityResponse
  faqs(filters: FaqFiltersInput, locale: I18NLocaleCode, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): FaqEntityResponseCollection
  footer(locale: I18NLocaleCode, publicationState: PublicationState = LIVE): FooterEntityResponse
  general(locale: I18NLocaleCode): GeneralEntityResponse
  homepage(locale: I18NLocaleCode, publicationState: PublicationState = LIVE): HomepageEntityResponse
  i18NLocale(id: ID): I18NLocaleEntityResponse
  i18NLocales(filters: I18NLocaleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): I18NLocaleEntityResponseCollection
  inbaArticle(id: ID, locale: I18NLocaleCode): InbaArticleEntityResponse
  inbaArticles(filters: InbaArticleFiltersInput, locale: I18NLocaleCode, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): InbaArticleEntityResponseCollection
  inbaRelease(id: ID): InbaReleaseEntityResponse
  inbaReleases(filters: InbaReleaseFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): InbaReleaseEntityResponseCollection
  inbaTag(id: ID, locale: I18NLocaleCode): InbaTagEntityResponse
  inbaTags(filters: InbaTagFiltersInput, locale: I18NLocaleCode, pagination: PaginationArg = {}, sort: [String] = []): InbaTagEntityResponseCollection
  me: UsersPermissionsMe
  menu(locale: I18NLocaleCode): MenuEntityResponse
  page(id: ID, locale: I18NLocaleCode): PageEntityResponse
  pageCategories(filters: PageCategoryFiltersInput, locale: I18NLocaleCode, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): PageCategoryEntityResponseCollection
  pageCategory(id: ID, locale: I18NLocaleCode): PageCategoryEntityResponse
  pages(filters: PageFiltersInput, locale: I18NLocaleCode, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): PageEntityResponseCollection
  regulation(id: ID): RegulationEntityResponse
  regulations(filters: RegulationFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): RegulationEntityResponseCollection
  tag(id: ID, locale: I18NLocaleCode): TagEntityResponse
  tags(filters: TagFiltersInput, locale: I18NLocaleCode, pagination: PaginationArg = {}, sort: [String] = []): TagEntityResponseCollection
  taxAdministratorsList: TaxAdministratorsListEntityResponse
  uploadFile(id: ID): UploadFileEntityResponse
  uploadFiles(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UploadFileEntityResponseCollection
  uploadFolder(id: ID): UploadFolderEntityResponse
  uploadFolders(filters: UploadFolderFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UploadFolderEntityResponseCollection
  usersPermissionsRole(id: ID): UsersPermissionsRoleEntityResponse
  usersPermissionsRoles(filters: UsersPermissionsRoleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UsersPermissionsRoleEntityResponseCollection
  usersPermissionsUser(id: ID): UsersPermissionsUserEntityResponse
  usersPermissionsUsers(filters: UsersPermissionsUserFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UsersPermissionsUserEntityResponseCollection
}

type Regulation {
  amending(filters: RegulationFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): RegulationRelationResponseCollection
  amendments(filters: RegulationFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): RegulationRelationResponseCollection
  attachments(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UploadFileRelationResponseCollection
  cancellation: RegulationEntityResponse
  cancelling(filters: RegulationFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): RegulationRelationResponseCollection
  category: ENUM_REGULATION_CATEGORY!
  createdAt: DateTime
  effectiveFrom: Date!
  fullTitle: String!
  isFullTextRegulation: Boolean!
  mainDocument: UploadFileEntityResponse!
  publishedAt: DateTime
  regNumber: String!
  slug: String!
  titleText: String
  updatedAt: DateTime
}

type RegulationEntity {
  attributes: Regulation
  id: ID
}

type RegulationEntityResponse {
  data: RegulationEntity
}

type RegulationEntityResponseCollection {
  data: [RegulationEntity!]!
  meta: ResponseCollectionMeta!
}

input RegulationFiltersInput {
  amending: RegulationFiltersInput
  amendments: RegulationFiltersInput
  and: [RegulationFiltersInput]
  cancellation: RegulationFiltersInput
  cancelling: RegulationFiltersInput
  category: StringFilterInput
  createdAt: DateTimeFilterInput
  effectiveFrom: DateFilterInput
  fullTitle: StringFilterInput
  id: IDFilterInput
  isFullTextRegulation: BooleanFilterInput
  not: RegulationFiltersInput
  or: [RegulationFiltersInput]
  publishedAt: DateTimeFilterInput
  regNumber: StringFilterInput
  slug: StringFilterInput
  titleText: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input RegulationInput {
  amending: [ID]
  amendments: [ID]
  attachments: [ID]
  cancellation: ID
  cancelling: [ID]
  category: ENUM_REGULATION_CATEGORY
  effectiveFrom: Date
  fullTitle: String
  isFullTextRegulation: Boolean
  mainDocument: ID
  publishedAt: DateTime
  regNumber: String
  slug: String
  titleText: String
}

type RegulationRelationResponseCollection {
  data: [RegulationEntity!]!
}

type ResponseCollectionMeta {
  pagination: Pagination!
}

input StringFilterInput {
  and: [String]
  between: [String]
  contains: String
  containsi: String
  endsWith: String
  eq: String
  eqi: String
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nei: String
  not: StringFilterInput
  notContains: String
  notContainsi: String
  notIn: [String]
  notNull: Boolean
  null: Boolean
  or: [String]
  startsWith: String
}

type Tag {
  articles(filters: ArticleFiltersInput, pagination: PaginationArg = {}, publicationState: PublicationState = LIVE, sort: [String] = []): ArticleRelationResponseCollection
  createdAt: DateTime
  locale: String
  localizations(filters: TagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): TagRelationResponseCollection
  pageCategory: PageCategoryEntityResponse
  title: String
  updatedAt: DateTime
}

type TagEntity {
  attributes: Tag
  id: ID
}

type TagEntityResponse {
  data: TagEntity
}

type TagEntityResponseCollection {
  data: [TagEntity!]!
  meta: ResponseCollectionMeta!
}

input TagFiltersInput {
  and: [TagFiltersInput]
  articles: ArticleFiltersInput
  createdAt: DateTimeFilterInput
  id: IDFilterInput
  locale: StringFilterInput
  localizations: TagFiltersInput
  not: TagFiltersInput
  or: [TagFiltersInput]
  pageCategory: PageCategoryFiltersInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input TagInput {
  articles: [ID]
  pageCategory: ID
  title: String
}

type TagRelationResponseCollection {
  data: [TagEntity!]!
}

type TaxAdministratorsList {
  createdAt: DateTime
  taxAdministrators(filters: ComponentTaxAdministratorsTaxAdministratorFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentTaxAdministratorsTaxAdministrator]!
  updatedAt: DateTime
}

type TaxAdministratorsListEntity {
  attributes: TaxAdministratorsList
  id: ID
}

type TaxAdministratorsListEntityResponse {
  data: TaxAdministratorsListEntity
}

type TaxAdministratorsListEntityResponseCollection {
  data: [TaxAdministratorsListEntity!]!
  meta: ResponseCollectionMeta!
}

input TaxAdministratorsListFiltersInput {
  and: [TaxAdministratorsListFiltersInput]
  createdAt: DateTimeFilterInput
  not: TaxAdministratorsListFiltersInput
  or: [TaxAdministratorsListFiltersInput]
  taxAdministrators: ComponentTaxAdministratorsTaxAdministratorFiltersInput
  updatedAt: DateTimeFilterInput
}

input TaxAdministratorsListInput {
  taxAdministrators: [ComponentTaxAdministratorsTaxAdministratorInput]
}

type TaxAdministratorsListRelationResponseCollection {
  data: [TaxAdministratorsListEntity!]!
}

"""A time string with format HH:mm:ss.SSS"""
scalar Time

input TimeFilterInput {
  and: [Time]
  between: [Time]
  contains: Time
  containsi: Time
  endsWith: Time
  eq: Time
  eqi: Time
  gt: Time
  gte: Time
  in: [Time]
  lt: Time
  lte: Time
  ne: Time
  nei: Time
  not: TimeFilterInput
  notContains: Time
  notContainsi: Time
  notIn: [Time]
  notNull: Boolean
  null: Boolean
  or: [Time]
  startsWith: Time
}

"""The `Upload` scalar type represents a file upload."""
scalar Upload

type UploadFile {
  alternativeText: String
  caption: String
  createdAt: DateTime
  ext: String
  formats: JSON
  hash: String!
  height: Int
  mime: String!
  name: String!
  previewUrl: String
  provider: String!
  provider_metadata: JSON
  related: [GenericMorph]
  size: Float!
  updatedAt: DateTime
  url: String!
  width: Int
}

type UploadFileEntity {
  attributes: UploadFile
  id: ID
}

type UploadFileEntityResponse {
  data: UploadFileEntity
}

type UploadFileEntityResponseCollection {
  data: [UploadFileEntity!]!
  meta: ResponseCollectionMeta!
}

input UploadFileFiltersInput {
  alternativeText: StringFilterInput
  and: [UploadFileFiltersInput]
  caption: StringFilterInput
  createdAt: DateTimeFilterInput
  ext: StringFilterInput
  folder: UploadFolderFiltersInput
  folderPath: StringFilterInput
  formats: JSONFilterInput
  hash: StringFilterInput
  height: IntFilterInput
  id: IDFilterInput
  mime: StringFilterInput
  name: StringFilterInput
  not: UploadFileFiltersInput
  or: [UploadFileFiltersInput]
  previewUrl: StringFilterInput
  provider: StringFilterInput
  provider_metadata: JSONFilterInput
  size: FloatFilterInput
  updatedAt: DateTimeFilterInput
  url: StringFilterInput
  width: IntFilterInput
}

input UploadFileInput {
  alternativeText: String
  caption: String
  ext: String
  folder: ID
  folderPath: String
  formats: JSON
  hash: String
  height: Int
  mime: String
  name: String
  previewUrl: String
  provider: String
  provider_metadata: JSON
  size: Float
  url: String
  width: Int
}

type UploadFileRelationResponseCollection {
  data: [UploadFileEntity!]!
}

type UploadFolder {
  children(filters: UploadFolderFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UploadFolderRelationResponseCollection
  createdAt: DateTime
  files(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UploadFileRelationResponseCollection
  name: String!
  parent: UploadFolderEntityResponse
  path: String!
  pathId: Int!
  updatedAt: DateTime
}

type UploadFolderEntity {
  attributes: UploadFolder
  id: ID
}

type UploadFolderEntityResponse {
  data: UploadFolderEntity
}

type UploadFolderEntityResponseCollection {
  data: [UploadFolderEntity!]!
  meta: ResponseCollectionMeta!
}

input UploadFolderFiltersInput {
  and: [UploadFolderFiltersInput]
  children: UploadFolderFiltersInput
  createdAt: DateTimeFilterInput
  files: UploadFileFiltersInput
  id: IDFilterInput
  name: StringFilterInput
  not: UploadFolderFiltersInput
  or: [UploadFolderFiltersInput]
  parent: UploadFolderFiltersInput
  path: StringFilterInput
  pathId: IntFilterInput
  updatedAt: DateTimeFilterInput
}

input UploadFolderInput {
  children: [ID]
  files: [ID]
  name: String
  parent: ID
  path: String
  pathId: Int
}

type UploadFolderRelationResponseCollection {
  data: [UploadFolderEntity!]!
}

type UsersPermissionsCreateRolePayload {
  ok: Boolean!
}

type UsersPermissionsDeleteRolePayload {
  ok: Boolean!
}

input UsersPermissionsLoginInput {
  identifier: String!
  password: String!
  provider: String! = "local"
}

type UsersPermissionsLoginPayload {
  jwt: String
  user: UsersPermissionsMe!
}

type UsersPermissionsMe {
  blocked: Boolean
  confirmed: Boolean
  email: String
  id: ID!
  role: UsersPermissionsMeRole
  username: String!
}

type UsersPermissionsMeRole {
  description: String
  id: ID!
  name: String!
  type: String
}

type UsersPermissionsPasswordPayload {
  ok: Boolean!
}

type UsersPermissionsPermission {
  action: String!
  createdAt: DateTime
  role: UsersPermissionsRoleEntityResponse
  updatedAt: DateTime
}

type UsersPermissionsPermissionEntity {
  attributes: UsersPermissionsPermission
  id: ID
}

type UsersPermissionsPermissionEntityResponse {
  data: UsersPermissionsPermissionEntity
}

type UsersPermissionsPermissionEntityResponseCollection {
  data: [UsersPermissionsPermissionEntity!]!
  meta: ResponseCollectionMeta!
}

input UsersPermissionsPermissionFiltersInput {
  action: StringFilterInput
  and: [UsersPermissionsPermissionFiltersInput]
  createdAt: DateTimeFilterInput
  id: IDFilterInput
  not: UsersPermissionsPermissionFiltersInput
  or: [UsersPermissionsPermissionFiltersInput]
  role: UsersPermissionsRoleFiltersInput
  updatedAt: DateTimeFilterInput
}

input UsersPermissionsPermissionInput {
  action: String
  role: ID
}

type UsersPermissionsPermissionRelationResponseCollection {
  data: [UsersPermissionsPermissionEntity!]!
}

input UsersPermissionsRegisterInput {
  email: String!
  password: String!
  username: String!
}

type UsersPermissionsRole {
  createdAt: DateTime
  description: String
  name: String!
  permissions(filters: UsersPermissionsPermissionFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UsersPermissionsPermissionRelationResponseCollection
  type: String
  updatedAt: DateTime
  users(filters: UsersPermissionsUserFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UsersPermissionsUserRelationResponseCollection
}

type UsersPermissionsRoleEntity {
  attributes: UsersPermissionsRole
  id: ID
}

type UsersPermissionsRoleEntityResponse {
  data: UsersPermissionsRoleEntity
}

type UsersPermissionsRoleEntityResponseCollection {
  data: [UsersPermissionsRoleEntity!]!
  meta: ResponseCollectionMeta!
}

input UsersPermissionsRoleFiltersInput {
  and: [UsersPermissionsRoleFiltersInput]
  createdAt: DateTimeFilterInput
  description: StringFilterInput
  id: IDFilterInput
  name: StringFilterInput
  not: UsersPermissionsRoleFiltersInput
  or: [UsersPermissionsRoleFiltersInput]
  permissions: UsersPermissionsPermissionFiltersInput
  type: StringFilterInput
  updatedAt: DateTimeFilterInput
  users: UsersPermissionsUserFiltersInput
}

input UsersPermissionsRoleInput {
  description: String
  name: String
  permissions: [ID]
  type: String
  users: [ID]
}

type UsersPermissionsRoleRelationResponseCollection {
  data: [UsersPermissionsRoleEntity!]!
}

type UsersPermissionsUpdateRolePayload {
  ok: Boolean!
}

type UsersPermissionsUser {
  blocked: Boolean
  confirmed: Boolean
  createdAt: DateTime
  email: String!
  provider: String
  role: UsersPermissionsRoleEntityResponse
  updatedAt: DateTime
  username: String!
}

type UsersPermissionsUserEntity {
  attributes: UsersPermissionsUser
  id: ID
}

type UsersPermissionsUserEntityResponse {
  data: UsersPermissionsUserEntity
}

type UsersPermissionsUserEntityResponseCollection {
  data: [UsersPermissionsUserEntity!]!
  meta: ResponseCollectionMeta!
}

input UsersPermissionsUserFiltersInput {
  and: [UsersPermissionsUserFiltersInput]
  blocked: BooleanFilterInput
  confirmationToken: StringFilterInput
  confirmed: BooleanFilterInput
  createdAt: DateTimeFilterInput
  email: StringFilterInput
  id: IDFilterInput
  not: UsersPermissionsUserFiltersInput
  or: [UsersPermissionsUserFiltersInput]
  password: StringFilterInput
  provider: StringFilterInput
  resetPasswordToken: StringFilterInput
  role: UsersPermissionsRoleFiltersInput
  updatedAt: DateTimeFilterInput
  username: StringFilterInput
}

input UsersPermissionsUserInput {
  blocked: Boolean
  confirmationToken: String
  confirmed: Boolean
  email: String
  password: String
  provider: String
  resetPasswordToken: String
  role: ID
  username: String
}

type UsersPermissionsUserRelationResponseCollection {
  data: [UsersPermissionsUserEntity!]!
}
