fragment DocumentCategoryEntity on DocumentCategoryEntity {
  id
  attributes {
    title
    slug
  }
}

fragment DocumentSlugEntity on DocumentEntity {
  __typename
  id
  attributes {
    slug
    title
  }
}

fragment DocumentEntity on DocumentEntity {
  ...DocumentSlugEntity
  attributes {
    publishedAt
    updatedAt
    documentCategory {
      data {
        ...DocumentCategoryEntity
      }
    }
    description
    files {
      data {
        ...UploadFileEntity
      }
    }
  }
}

query DocumentBySlug($slug: String!) {
  documents(filters: { slug: { eq: $slug } }) {
    data {
      ...DocumentEntity
    }
  }
}
