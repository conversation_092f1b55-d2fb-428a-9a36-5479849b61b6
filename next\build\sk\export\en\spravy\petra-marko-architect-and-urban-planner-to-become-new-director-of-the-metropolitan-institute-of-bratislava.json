{"pageProps": {"general": {"general": {"data": {"attributes": {"header": {"links": [{"label": "Contacts", "page": {"data": {"id": "641", "attributes": {"slug": "visiting-and-office-hours", "title": "Visiting and Office Hours", "locale": "en"}}}, "url": null, "analyticsId": null, "showOnDesktop": true, "showOnMobile": true, "icon": "kontakt"}, {"label": "Я з України", "page": {"data": {"id": "636", "attributes": {"slug": "братислава-для-украiни", "title": "Братислава для України", "locale": "en"}}}, "url": null, "analyticsId": null, "showOnDesktop": false, "showOnMobile": true, "icon": "ukraina"}, {"label": "I am a tourist", "page": {"data": null}, "url": "https://www.visitbratislava.com/", "analyticsId": null, "showOnDesktop": false, "showOnMobile": true, "icon": "som_turista"}], "accountLink": null}, "newsPage": {"data": {"id": "377", "attributes": {"title": "News", "slug": "city-of-bratislava/transparent-city/news", "locale": "en", "parentPage": {"data": {"attributes": {"slug": "city-of-bratislava/transparent-city", "locale": "en", "title": "Transparent City", "parentPage": {"data": null}}}}}}}, "officialBoardPage": {"data": null}, "privacyPolicyPage": {"data": null}, "vznPage": {"data": null}, "inbaPage": {"data": null}, "inbaReleasesPage": {"data": null}}}}, "menu": {"data": {"attributes": {"menus": [{"id": "7", "label": "City of \nBratislava", "icon": "mesto_01", "page": {"data": {"id": "924", "attributes": {"slug": "city-of-bratislava", "title": "City of Bratislava", "locale": "en"}}}, "sections": [{"id": "41", "label": "City Administration", "icon": "sprava_mesta_01", "page": {"data": {"id": "349", "attributes": {"slug": "city-of-bratislava/city-administration", "title": "City Administration", "locale": "en"}}}, "links": [{"id": "108", "label": "Elected Bodies", "page": {"data": {"id": "350", "attributes": {"slug": "city-of-bratislava/city-administration/elected-bodies", "title": "Elected Bodies", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "110", "label": "City Hall", "page": {"data": {"id": "354", "attributes": {"slug": "city-of-bratislava/city-administration/city-hall", "title": "City Hall", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "102", "label": "City Owned Entities", "page": {"data": {"id": "358", "attributes": {"slug": "city-of-bratislava/city-administration/city-owned-entities", "title": "City Owned Entities", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "37", "label": "Transparent City", "icon": "transparentne_mesto_01", "page": {"data": {"id": "376", "attributes": {"slug": "city-of-bratislava/transparent-city", "title": "Transparent City", "locale": "en"}}}, "links": [{"id": "104", "label": "News", "page": {"data": {"id": "377", "attributes": {"slug": "city-of-bratislava/transparent-city/news", "title": "News", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "103", "label": "Official Noticeboard", "page": {"data": {"id": "378", "attributes": {"slug": "city-of-bratislava/transparent-city/official-noticeboard", "title": "Official Noticeboard", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "105", "label": "Access to Information", "page": {"data": {"id": "379", "attributes": {"slug": "city-of-bratislava/transparent-city/access-to-information", "title": "Access to Information", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "35", "label": "Taxes and Levies", "icon": "dane_01", "page": {"data": {"id": "422", "attributes": {"slug": "city-of-bratislava/taxes-and-levies", "title": "Taxes and Levies", "locale": "en"}}}, "links": [{"id": "106", "label": "Property tax", "page": {"data": {"id": "423", "attributes": {"slug": "city-of-bratislava/taxes-and-levies/property-tax", "title": "Property Tax", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "227", "label": "Online property tax payment", "page": {"data": null}, "url": "en/city-of-bratislava/taxes-and-levies/online-property-tax-payment", "analyticsId": null}, {"id": "228", "label": "Levies for Municipal Waste and Small Construction Waste", "page": {"data": null}, "url": "en/city-of-bratislava/taxes-and-levies/levies-for-municipal-waste-and-small-construction-waste", "analyticsId": null}]}, {"id": "40", "label": "Projects", "icon": "projekty_01", "page": {"data": {"id": "428", "attributes": {"slug": "city-of-bratislava/projects", "title": "Projects", "locale": "en"}}}, "links": [{"id": "111", "label": "Vivid <PERSON>", "page": {"data": {"id": "527", "attributes": {"slug": "city-of-bratislava/projects/vivid-places", "title": "Vivid <PERSON>", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "112", "label": "EU Projects", "page": {"data": {"id": "834", "attributes": {"slug": "city-of-bratislava/projects/eu-projects", "title": "EU Projects", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "113", "label": "Transport Projects", "page": {"data": {"id": "448", "attributes": {"slug": "transport-and-maps/transport/transport-projects", "title": "Transport Projects", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "36", "label": "Partnership Opportunities", "icon": "partnerstva_01", "page": {"data": {"id": "429", "attributes": {"slug": "city-of-bratislava/partnership-opportunities", "title": "Partnership Opportunities", "locale": "en"}}}, "links": [{"id": "114", "label": "Vivid <PERSON>", "page": {"data": {"id": "430", "attributes": {"slug": "city-of-bratislava/partnership-opportunities/vivid-places", "title": "Vivid <PERSON>", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "115", "label": "10 000 Trees", "page": {"data": {"id": "431", "attributes": {"slug": "city-of-bratislava/partnership-opportunities/10000-trees", "title": "10 000 Trees", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "116", "label": "City Museum", "page": {"data": {"id": "432", "attributes": {"slug": "city-of-bratislava/partnership-opportunities/city-museum", "title": "City Museum", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "38", "label": "Bratislava City Archives", "icon": "dedicstvo_06", "page": {"data": {"id": "876", "attributes": {"slug": "city-of-bratislava/bratislava-city-archive", "title": " Bratislava City Archives", "locale": "en"}}}, "links": [{"id": "117", "label": "About City Archive", "page": {"data": {"id": "877", "attributes": {"slug": "city-of-bratislava/bratislava-city-archive/about-city-archive", "title": "About City Archive", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "118", "label": "Archival Utilities", "page": {"data": {"id": "403", "attributes": {"slug": "city-of-bratislava/bratislava-city-archive/archival-utilities", "title": "Archival Utilities", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "119", "label": "Archival funds", "page": {"data": {"id": "878", "attributes": {"slug": "city-of-bratislava/bratislava-city-archive/archival-funds", "title": "Archival funds", "locale": "en"}}}, "url": null, "analyticsId": null}]}]}, {"id": "8", "label": "Transport\nand Maps", "icon": "doprava_mapy_02", "page": {"data": {"id": "925", "attributes": {"slug": "transport-and-maps", "title": "Transport and Maps", "locale": "en"}}}, "sections": [{"id": "39", "label": "Road Administration and Maintenance", "icon": "sprava_a_udrzba_02", "page": {"data": {"id": "436", "attributes": {"slug": "transport-and-maps/road-administration-and-maintenance", "title": "Road Administration and Maintenance", "locale": "en"}}}, "links": [{"id": "120", "label": "Restrictions and Disorders", "page": {"data": {"id": "437", "attributes": {"slug": "transport-and-maps/road-administration-and-maintenance/restrictions-and-disorders", "title": "Restrictions and Disorders", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "122", "label": "Winter Maintenance", "page": {"data": {"id": "438", "attributes": {"slug": "transport-and-maps/road-administration-and-maintenance/winter-maintenance", "title": "Winter Maintenance", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "47", "label": "Transport", "icon": "doprava_02", "page": {"data": {"id": "445", "attributes": {"slug": "transport-and-maps/transport", "title": "Transport", "locale": "en"}}}, "links": [{"id": "123", "label": "Territorial Transport Master Plan", "page": {"data": {"id": "446", "attributes": {"slug": "transport-and-maps/transport/territorial-transport-master-plan", "title": "Territorial Transport Master Plan", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "124", "label": "Traffic Permits", "page": {"data": {"id": "447", "attributes": {"slug": "transport-and-maps/transport/traffic-permits", "title": "Traffic Permits", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "125", "label": "Integrated Transport System", "page": {"data": {"id": "455", "attributes": {"slug": "transport-and-maps/transport/integrated-transport-system", "title": "Integrated Transport System", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "43", "label": "Parking", "icon": "parkovanie_02", "page": {"data": {"id": "450", "attributes": {"slug": "transport-and-maps/parking", "title": "Parking", "locale": "en"}}}, "links": [{"id": "126", "label": "Towing of Vehicles", "page": {"data": {"id": "451", "attributes": {"slug": "transport-and-maps/parking/towing-of-vehicles", "title": "Towing of Vehicles", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "127", "label": "Regulated Parking System PAAS", "page": {"data": {"id": "452", "attributes": {"slug": "transport-and-maps/parking/regulated-parking-system-paas", "title": "Regulated Parking System PAAS", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "128", "label": "The Old Town Micro-Zone", "page": {"data": {"id": "826", "attributes": {"slug": "transport-and-maps/parking/the-old-town-micro-zone", "title": "The Old Town Micro-Zone", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "46", "label": "Cycling", "icon": "cyklo_02", "page": {"data": {"id": "456", "attributes": {"slug": "transport-and-maps/cycling", "title": "Cycling", "locale": "en"}}}, "links": [{"id": "129", "label": "Map", "page": {"data": {"id": "457", "attributes": {"slug": "transport-and-maps/cycling/map", "title": "Map", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "131", "label": "Bicycle Counters", "page": {"data": {"id": "460", "attributes": {"slug": "transport-and-maps/cycling/bicycle-counters", "title": "Bicycle Counters", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "225", "label": "Cycle routes", "page": {"data": {"id": "1093", "attributes": {"slug": "transport-and-maps/cycling/cycle-routes", "title": "Cycle routes", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "42", "label": "Shared Mobility", "icon": "zdielana_mobilita_02", "page": {"data": {"id": "499", "attributes": {"slug": "transport-and-maps/shared-mobility", "title": "Shared Mobility", "locale": "en"}}}, "links": [{"id": "132", "label": "Bikesharing", "page": {"data": {"id": "503", "attributes": {"slug": "transport-and-maps/shared-mobility/bikesharing", "title": "Bikesharing", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "133", "label": "Scooters", "page": {"data": {"id": "505", "attributes": {"slug": "transport-and-maps/shared-mobility/scooters", "title": "Scooters", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "134", "label": "<PERSON><PERSON><PERSON>", "page": {"data": {"id": "509", "attributes": {"slug": "transport-and-maps/shared-mobility/blinkee", "title": "<PERSON><PERSON><PERSON>", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "45", "label": " Maps", "icon": "mapy_02", "page": {"data": {"id": "479", "attributes": {"slug": "transport-and-maps/maps", "title": "Maps", "locale": "en"}}}, "links": [{"id": "135", "label": "Geoportal", "page": {"data": null}, "url": "https://geoportal.bratislava.sk/pfa/apps/sites/#/verejny-mapovy-portal", "analyticsId": null}, {"id": "136", "label": "Greenery Care Map", "page": {"data": {"id": "540", "attributes": {"slug": "environment-and-construction/greenery/greenery-creation-and-maintenance/trees-in-the-city/greenery-care", "title": "Greenery Care", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "137", "label": "City Spacial Plan", "page": {"data": {"id": "593", "attributes": {"slug": "environment-and-construction/development-of-the-city/spatial-planning-documentation/current-spatial-planning-documents/city-spacial-plan-with-amendments", "title": "City Spacial Plan with Amendments", "locale": "en"}}}, "url": null, "analyticsId": null}]}]}, {"id": "11", "label": "Environment \nand Construction", "icon": "zp_vystavba_03", "page": {"data": {"id": "929", "attributes": {"slug": "environment-and-construction", "title": "Environment and Construction", "locale": "en"}}}, "sections": [{"id": "44", "label": "Environment", "icon": "zivotne_prostredie_03", "page": {"data": {"id": "461", "attributes": {"slug": "environment-and-construction/environment", "title": "Environment", "locale": "en"}}}, "links": [{"id": "138", "label": "Waste", "page": {"data": {"id": "462", "attributes": {"slug": "environment-and-construction/environment/waste", "title": "Waste", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "139", "label": "Air Protection", "page": {"data": {"id": "471", "attributes": {"slug": "environment-and-construction/environment/air-protection", "title": "Air Protection", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "140", "label": "Removal of Abandoned Vehicles", "page": {"data": {"id": "524", "attributes": {"slug": "environment-and-construction/environment/removal-of-abandoned-vehicles", "title": "Removal of Abandoned Vehicles", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "48", "label": "Greenery", "icon": "zelen_03", "page": {"data": {"id": "531", "attributes": {"slug": "environment-and-construction/greenery", "title": "Greenery", "locale": "en"}}}, "links": [{"id": "141", "label": "Greenery Creation and Maintenance", "page": {"data": {"id": "533", "attributes": {"slug": "environment-and-construction/greenery/greenery-creation-and-maintenance", "title": "Greenery Creation and Maintenance", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "142", "label": "City Forestry", "page": {"data": {"id": "556", "attributes": {"slug": "environment-and-construction/greenery/city-forestry", "title": "City Forestry", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "54", "label": "Public Lighting", "icon": "verejne_osvetlenie_03", "page": {"data": {"id": "569", "attributes": {"slug": "environment-and-construction/public-lighting", "title": "Public lighting", "locale": "en"}}}, "links": [{"id": "143", "label": "<PERSON><PERSON><PERSON><PERSON>", "page": {"data": {"id": "569", "attributes": {"slug": "environment-and-construction/public-lighting", "title": "Public lighting", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "144", "label": "Information for Designers and Builders", "page": {"data": {"id": "574", "attributes": {"slug": "environment-and-construction/public-lighting/information-for-designers-and-builders", "title": "Information for Designers and Builders", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "145", "label": "Rental of Public Lighting Poles", "page": {"data": {"id": "575", "attributes": {"slug": "environment-and-construction/public-lighting/rental-of-public-lighting-poles", "title": "Rental of Public Lighting Poles", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "50", "label": "Development of the City", "icon": "rozvoj_mesta_03", "page": {"data": {"id": "589", "attributes": {"slug": "environment-and-construction/development-of-the-city", "title": "Development of the City", "locale": "en"}}}, "links": [{"id": "146", "label": "Spatial Planning Documentation", "page": {"data": {"id": "591", "attributes": {"slug": "environment-and-construction/development-of-the-city/spatial-planning-documentation", "title": "Spatial Planning Documentation", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "147", "label": "Construction Guidelines", "page": {"data": {"id": "607", "attributes": {"slug": "environment-and-construction/development-of-the-city/construction-guidelines", "title": "Construction Guidelines", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "148", "label": "The Chief Architect", "page": {"data": {"id": "689", "attributes": {"slug": "environment-and-construction/development-of-the-city/the-chief-architect", "title": "The Chief Architect", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "75", "label": "Climate", "icon": "klima_03", "page": {"data": {"id": "1080", "attributes": {"slug": "enviroment-and-construction/climate", "title": "Climate", "locale": "en"}}}, "links": [{"id": "222", "label": "Climate Plan", "page": {"data": {"id": "1079", "attributes": {"slug": "enviroment-and-construction/climate/climate-plan", "title": "Climate Plan", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "224", "label": "Climate Challenge", "page": {"data": {"id": "1083", "attributes": {"slug": "enviroment-and-construction/climate/bratislava-mayors-climate-challenge", "title": "Bratislava Mayor's Climate Challenge", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "223", "label": "Climate Office", "page": {"data": {"id": "1081", "attributes": {"slug": "enviroment-and-construction/climate/climate-office", "title": "Climate Office", "locale": "en"}}}, "url": null, "analyticsId": null}]}]}, {"id": "9", "label": "Social Services\nand Housing", "icon": "socialna_pomoc_04", "page": {"data": {"id": "928", "attributes": {"slug": "social-services-and-housing", "title": "Social Services and Housing", "locale": "en"}}}, "sections": [{"id": "53", "label": "Housing and Accommodation", "icon": "byvanie_04", "page": {"data": {"id": "481", "attributes": {"slug": "social-services-and-housing/housing-and-accommodation", "title": "Housing and Accommodation", "locale": "en"}}}, "links": [{"id": "149", "label": "Social Housing", "page": {"data": {"id": "482", "attributes": {"slug": "social-services-and-housing/housing-and-accommodation/social-housing", "title": "Social Housing", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "150", "label": "Municipal Dormitories", "page": {"data": {"id": "492", "attributes": {"slug": "social-services-and-housing/housing-and-accommodation/municipal-dormitories", "title": "Municipal Dormitories", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "151", "label": "Crisis Accommodation Facilities", "page": {"data": {"id": "493", "attributes": {"slug": "social-services-and-housing/housing-and-accommodation/crisis-accommodation-facilities", "title": "Emergency Accommodation", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "51", "label": "Social Services and Facilities", "icon": "sluzby_04", "page": {"data": {"id": "514", "attributes": {"slug": "social-services-and-housing/social-services-and-facilities", "title": "Social Services and Facilities", "locale": "en"}}}, "links": [{"id": "155", "label": "Services and Facilities for Seniors", "page": {"data": {"id": "513", "attributes": {"slug": "social-services-and-housing/social-services-and-facilities/facilities-for-seniors", "title": "Facilities and services for seniors", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "156", "label": "The City Streetwork Team", "page": {"data": {"id": "500", "attributes": {"slug": "social-services-and-housing/social-facilities/city-streetwork-team", "title": "The City Streetwork Team", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "202", "label": "REPULS Centre for Children and Families", "page": {"data": {"id": "521", "attributes": {"slug": "social-services-and-housing/social-services-and-facilities/repuls", "title": "REPULS Centre for Children and Families ", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "52", "label": "Help and Support", "icon": "pomoc_04", "page": {"data": {"id": "502", "attributes": {"slug": "social-services-and-housing/help-and-support", "title": "Help and Support", "locale": "en"}}}, "links": [{"id": "158", "label": "Forms of Financial Support", "page": {"data": {"id": "510", "attributes": {"slug": "social-services-and-housing/help-and-support/forms-of-financial-support", "title": "Forms of Financial Support", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "159", "label": "Homeless Accommodation and Shelters", "page": {"data": {"id": "508", "attributes": {"slug": "social-services-and-housing/housing-and-accomodation/homeless-accommodation-and-shelters", "title": "Homeless Accommodation and Shelters", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "160", "label": "Other Forms of Assistance", "page": {"data": {"id": "507", "attributes": {"slug": "social-services-and-housing/help-and-support/assistance-meals-clothing-washrooms", "title": "Assistance in Providing of Meals, Clothing and Access to Washrooms", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "55", "label": "Financial Aid for Organizations", "icon": "dotacie_05", "page": {"data": {"id": "529", "attributes": {"slug": "social-services-and-housing/financial-aid", "title": "Financial Aid", "locale": "en"}}}, "links": [{"id": "161", "label": "Financing of Social Services Providers", "page": {"data": {"id": "530", "attributes": {"slug": "social-services-and-housing/financial-aid/financing-of-private-social-services-providers", "title": "Financing of Private Social Services Providers", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "162", "label": "The Bratislava for All Social Grant Program", "page": {"data": {"id": "537", "attributes": {"slug": "social-services-and-housing/financial-help/the-bratislava-for-all-social-grant-program", "title": "The Bratislava for All Social Grant Program", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "56", "label": "Social Activities", "icon": "aktivity_04", "page": {"data": {"id": "480", "attributes": {"slug": "social-services-and-housing/social-activities", "title": "Social Activities", "locale": "en"}}}, "links": [{"id": "164", "label": "The Section of Social Affairs", "page": {"data": {"id": "501", "attributes": {"slug": "social-services-and-housing/social-activities/the-section-of-social-affairs", "title": "The Section of Social Affairs", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "165", "label": "The City Mobile Team", "page": {"data": {"id": "500", "attributes": {"slug": "social-services-and-housing/social-facilities/city-streetwork-team", "title": "The City Streetwork Team", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "166", "label": "Senior Citizens' Organisations", "page": {"data": {"id": "497", "attributes": {"slug": "social-services-and-housing/social-activities/senior-citizens-organisations", "title": "Active Ageing", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "69", "label": "Barrier-free city", "icon": "pomoc_04", "page": {"data": null}, "links": [{"id": "208", "label": "Information On City’s Accessibility", "page": {"data": {"id": "962", "attributes": {"slug": "social-services-and-housing/social-activities/information-on-city’s-accessibility", "title": "Information on city’s accessibility ", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "209", "label": "Barrier-Free Communication", "page": {"data": {"id": "518", "attributes": {"slug": "social-services-and-housing/social-services/barrier-free-communication", "title": "Barrier-Free Communication", "locale": "en"}}}, "url": null, "analyticsId": null}]}]}, {"id": "10", "label": "Education \nand Leisure", "icon": "vzdelavanie_05", "page": {"data": {"id": "927", "attributes": {"slug": "education-and-leisure", "title": "Education and Leisure", "locale": "en"}}}, "sections": [{"id": "57", "label": "Education", "icon": "skolstvo_05", "page": {"data": {"id": "555", "attributes": {"slug": "education-and-leisure/education", "title": "Education", "locale": "en"}}}, "links": [{"id": "167", "label": "Art Schools", "page": {"data": {"id": "557", "attributes": {"slug": "education-and-leisure/education/art-schools", "title": "Art Schools", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "168", "label": "Leisure Centres", "page": {"data": {"id": "559", "attributes": {"slug": "education-and-leisure/education/leisure-centres", "title": "Leisure Centres", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "169", "label": "Private Schools", "page": {"data": {"id": "562", "attributes": {"slug": "education-and-leisure/education/private-schools", "title": "Private Schools", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "60", "label": "Sports", "icon": "sport_05", "page": {"data": {"id": "566", "attributes": {"slug": "education-and-leisure/sports", "title": "Sports", "locale": "en"}}}, "links": [{"id": "170", "label": "Events", "page": {"data": {"id": "1123", "attributes": {"slug": "education-and-leisure/sport/events", "title": "Events", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "217", "label": "Camping", "page": {"data": {"id": "1021", "attributes": {"slug": "education-and-leisure/sport/camping", "title": "Camping", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "172", "label": "<PERSON><PERSON>ko Outdoor Gyms", "page": {"data": {"id": "912", "attributes": {"slug": "education-and-leisure/sports/cvicko-outdoor-gyms", "title": "<PERSON><PERSON>ko Outdoor Gyms", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "58", "label": "Children and Youth", "icon": "deti_a_mladez_05", "page": {"data": {"id": "576", "attributes": {"slug": "education-and-leisure/children-and-youth", "title": "Children and Youth", "locale": "en"}}}, "links": [{"id": "173", "label": "The City Youth Parliament", "page": {"data": {"id": "577", "attributes": {"slug": "education-and-leisure/children-and-youth/city-youth-parliament", "title": "City Youth Parliament", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "174", "label": "Children for Bratislava", "page": {"data": {"id": "578", "attributes": {"slug": "education-and-leisure/children-and-youth/children-for-bratislava", "title": "Children for Bratislava", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "175", "label": "Youth Development Strategy", "page": {"data": {"id": "579", "attributes": {"slug": "education-and-leisure/children-and-youth/youth-development-strategy", "title": "Youth Development Strategy", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "59", "label": "Awards", "icon": "ocenovanie_05", "page": {"data": {"id": "580", "attributes": {"slug": "education-and-leisure/awards", "title": "Awards", "locale": "en"}}}, "links": [{"id": "176", "label": "Talented Young People", "page": {"data": {"id": "581", "attributes": {"slug": "education-and-leisure/awards/talented-young-people", "title": "Talented Young People", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "177", "label": "The Bratislava Sport Award", "page": {"data": {"id": "582", "attributes": {"slug": "education-and-leisure/awards/bratislava-sports-award", "title": "The Bratislava Sports Award", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "178", "label": "Teachers' Day", "page": {"data": {"id": "583", "attributes": {"slug": "education-and-leisure/awards/teachers-day", "title": "Teachers' Day ", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "62", "label": "Subsidies", "icon": "dotacie_05", "page": {"data": {"id": "584", "attributes": {"slug": "education-and-leisure/subsidies", "title": "Subsidies", "locale": "en"}}}, "links": [{"id": "179", "label": "Sub-program 1", "page": {"data": {"id": "585", "attributes": {"slug": "education-and-leisure/subsidies/subprogramme-1", "title": "Sub-programme 1", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "180", "label": "Sub-program 2", "page": {"data": {"id": "586", "attributes": {"slug": "education-and-leisure/subsidies/subprogramme-2", "title": "Sub-programme 2", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "181", "label": "Sub-program 4", "page": {"data": {"id": "588", "attributes": {"slug": "education-and-leisure/subsidies/subprogramme-4", "title": "Sub-programme 4 ", "locale": "en"}}}, "url": null, "analyticsId": null}]}]}, {"id": "12", "label": "Culture\nand Communities", "icon": "kultura_06", "page": {"data": {"id": "926", "attributes": {"slug": "culture-and-communities", "title": "Culture and Communities", "locale": "en"}}}, "sections": [{"id": "63", "label": "Events", "icon": "kalendar_06", "page": {"data": {"id": "613", "attributes": {"slug": "culture-and-communities/events", "title": "Events", "locale": "en"}}}, "links": [{"id": "182", "label": "The Calendar of Events", "page": {"data": null}, "url": "https://www.bkis.sk/podujatia/", "analyticsId": null}, {"id": "183", "label": "Events Organised by the City", "page": {"data": {"id": "615", "attributes": {"slug": "culture-and-communities/events/events-organised-by-the-city", "title": "Events Organised by the City", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "184", "label": "COVID Measures for Culture", "page": {"data": {"id": "631", "attributes": {"slug": "culture-and-communities/events/covid-measures-for-culture", "title": "COVID Measures for Culture", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "65", "label": "The City's Cultural Organisations", "icon": "organizacie_06", "page": {"data": {"id": "628", "attributes": {"slug": "culture-and-communities/the-citys-cultural-organisations", "title": "The City's Cultural Organisations", "locale": "en"}}}, "links": [{"id": "185", "label": "City Gallery", "page": {"data": {"id": "433", "attributes": {"slug": "culture-and-communities/the-citys-cultural-organisations/city-gallery", "title": "City Gallery", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "186", "label": "City Museum", "page": {"data": {"id": "406", "attributes": {"slug": "culture-and-communities/the-citys-cultural-organisations/city-museum", "title": "City Museum", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "187", "label": "City Library", "page": {"data": {"id": "408", "attributes": {"slug": "culture-and-communities/the-citys-cultural-organisations/city-library", "title": "City Library", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "61", "label": "The Approach to Culture", "icon": "koncepcia_06", "page": {"data": {"id": "618", "attributes": {"slug": "culture-and-communities/the-approach-to-culture", "title": "The Approach to Culture", "locale": "en"}}}, "links": [{"id": "188", "label": "Department of Culture", "page": {"data": {"id": "619", "attributes": {"slug": "culture-and-communities/the-approach-to-culture/the-department-of-culture", "title": "The Department of Culture", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "189", "label": "Expert Committees", "page": {"data": {"id": "620", "attributes": {"slug": "culture-and-communities/the-approach-to-culture/expert-committees", "title": "Expert Committees", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "190", "label": "Cultural Projects", "page": {"data": {"id": "621", "attributes": {"slug": "culture-and-communities/the-approach-to-culture/cultural-projects", "title": "Cultural Projects", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "64", "label": "Cultural Services", "icon": "sluzby_06", "page": {"data": {"id": "622", "attributes": {"slug": "culture-and-communities/cultural-services", "title": "Cultural Services", "locale": "en"}}}, "links": [{"id": "191", "label": "The Bratislava City Foundation", "page": {"data": {"id": "623", "attributes": {"slug": "culture-and-communities/cultural-services/bratislava-city-foundation", "title": "Bratislava City Foundation", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "192", "label": "Support of Strategic Cultural Events", "page": {"data": {"id": "396", "attributes": {"slug": "culture-and-communities/cultural-services/support-of-strategic-cultural-events", "title": "Support of Strategic Cultural Events", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "193", "label": "Support of Cultural and Community Events", "page": {"data": {"id": "624", "attributes": {"slug": "culture-and-communities/cultural-services/support-of-cultural-and-community-events", "title": "Support of Cultural and Community Events", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "66", "label": "Cultural Heritage", "icon": "dedicstvo_06", "page": {"data": {"id": "549", "attributes": {"slug": "culture-and-communities/cultural-heritage", "title": "Cultural Heritage", "locale": "en"}}}, "links": [{"id": "194", "label": "National cultural monuments", "page": {"data": {"id": "409", "attributes": {"slug": "culture-and-communities/the-citys-cultural-organisations/the-municipal-monument-preservation-institute", "title": "The Municipal Monument Preservation Institute", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "195", "label": "Historic sites", "page": {"data": {"id": "549", "attributes": {"slug": "culture-and-communities/cultural-heritage", "title": "Cultural Heritage", "locale": "en"}}}, "url": null, "analyticsId": null}, {"id": "196", "label": "Works of art in public spaces", "page": {"data": {"id": "549", "attributes": {"slug": "culture-and-communities/cultural-heritage", "title": "Cultural Heritage", "locale": "en"}}}, "url": null, "analyticsId": null}]}, {"id": "67", "label": " Communities", "icon": "komunity_06", "page": {"data": {"id": "632", "attributes": {"slug": "culture-and-communities/communities", "title": "Communities", "locale": "en"}}}, "links": [{"id": "226", "label": "Assistance center for people of foreign origin", "page": {"data": {"id": "796", "attributes": {"slug": "culture-and-communities/communities/assistance-centre-for-people-of-foreign-origin", "title": "Assistance center for people of foreign origin - LOOM", "locale": "en"}}}, "url": null, "analyticsId": null}]}]}]}}}, "footer": {"data": {"attributes": {"facebookUrl": "https://www.facebook.com/Bratislava.sk/", "instagramUrl": "https://www.instagram.com/bratislava.sk/?hl=en", "columns": [{"title": "City of Bratislava", "links": [{"label": "Job opportunities", "page": {"data": null}, "article": {"data": null}, "url": "https://ats.nalgoo.com/sk/gate/bratislava/positions", "analyticsId": null}, {"label": "Organizational structure", "page": {"data": {"id": "356", "attributes": {"slug": "city-of-bratislava/city-administration/city-hall/organisational-structure-and-contact-details", "title": "Organisational Structure and Contact Details", "locale": "en"}}}, "article": {"data": null}, "url": null, "analyticsId": null}, {"label": "Visiting and office hours", "page": {"data": {"id": "641", "attributes": {"slug": "visiting-and-office-hours", "title": "Visiting and Office Hours", "locale": "en"}}}, "article": {"data": null}, "url": null, "analyticsId": null}, {"label": "Bratislava for Ukraine", "page": {"data": {"id": "635", "attributes": {"slug": "bratislava-for-ukraine", "title": "Bratislava for Ukraine", "locale": "en"}}}, "article": {"data": null}, "url": null, "analyticsId": null}, {"label": "Premises of the Primate's Palace for rent", "page": {"data": {"id": "1075", "attributes": {"slug": "city-of-bratislava/transparent-city/city-property/primates-palace/renting-primates-palace", "title": "Renting the Primate’s Palace", "locale": "en"}}}, "article": {"data": null}, "url": null, "analyticsId": null}, {"label": "Gallery of the <PERSON>rimate's Palace and its rare tapestries", "page": {"data": {"id": "1074", "attributes": {"slug": "city-of-bratislava/transparent-city/city-property/primates-palace/gallery", "title": "Gallery", "locale": "en"}}}, "article": {"data": null}, "url": null, "analyticsId": null}]}, {"title": "Quick links", "links": [{"label": "Bratislava ID", "page": {"data": null}, "article": {"data": null}, "url": "https://cutt.ly/G7tXJCy", "analyticsId": null}, {"label": "Privacy Policy", "page": {"data": {"id": "766", "attributes": {"slug": "privacy-policy", "title": "Privacy Policy", "locale": "en"}}}, "article": {"data": null}, "url": null, "analyticsId": null}, {"label": "GitHub", "page": {"data": null}, "article": {"data": null}, "url": "https://github.com/bratislava/bratislava.sk", "analyticsId": null}, {"label": "Plausible", "page": {"data": null}, "article": {"data": null}, "url": "https://plausible.io/bratislava.sk/", "analyticsId": null}]}], "accessibilityPageLink": {"label": "Accessibility statement", "page": {"data": {"id": "767", "attributes": {"slug": "accessibility-statement", "title": "Accessibility Statement", "locale": "en"}}}, "article": {"data": null}, "url": null, "analyticsId": null}, "innovationsLink": {"label": "Inovácie mesta Bratislava", "page": {"data": null}, "article": {"data": null}, "url": "https://inovacie.bratislava.sk/", "analyticsId": null}, "contactText": "<PERSON><PERSON><PERSON><PERSON> mesto Slovenskej republiky Bratislava\nPrimaciálne námestie 1\n814 99 Bratislava\n\nIČO: 00603481\nDIČ: 2020372596\nVAT IČ: SK2020372596\n\nMedia contact: <EMAIL>\nEmail: <EMAIL>\nInfoline 8:30-16:00: [+421 904 099 004](tel:+421904099004)\nWebsite questions: <EMAIL>"}}}, "alert": {"data": {"attributes": {"updatedAt": "2025-06-01T16:19:32.305Z", "text": ""}}}}, "slug": "petra-marko-architect-and-urban-planner-to-become-new-director-of-the-metropolitan-institute-of-bratislava", "article": {"__typename": "ArticleEntity", "id": "817", "attributes": {"slug": "petra-marko-architect-and-urban-planner-to-become-new-director-of-the-metropolitan-institute-of-bratislava", "title": "<PERSON>, architect and urban planner, to become new director of the Metropolitan Institute of Bratislava", "locale": "en", "perex": "Bratislava, 14 December 2023 – The City Council approved the outcome of the selection procedure for the position of Director of the Metropolitan Institute of Bratislava (MIB). Architect and urban planner <PERSON> has become the new head of the city's contributory organisation, responsible for architecture, zoning, participation and strategic planning. She will head the MIB from mid-January 2024, when she will replace the current director, <PERSON>.", "addedAt": "2023-12-14T13:08:00.000Z", "coverMedia": {"data": {"id": "5266", "attributes": {"url": "https://cdn-api.bratislava.sk/strapi-homepage/upload/<PERSON>_<PERSON>_autor_fotografie_<PERSON><PERSON>_<PERSON>_8c40acce5c.jpg", "width": 3644, "height": 3644, "caption": null, "alternativeText": "Nová riaditeľka Metropolitného inštitútu Bratislavy <PERSON>", "name": "<PERSON>_autor fotografie_Marek <PERSON>.jpg"}}}, "tag": {"data": {"id": "74", "attributes": {"title": "City Owned Entities", "pageCategory": {"data": {"id": "4", "attributes": {"title": "City of\nBratislava ", "color": "red"}}}}}}, "alias": null, "content": "_\"I consider <PERSON> to be an experienced architect and urban planner with many years of experience from abroad. I find her as someone who has much to offer in these areas. It is apparent from the vision she presented at the public hearing that she has clear goals to maintain the Institute's position as a think tank, to run it well in terms of management, and to move forward as a sustainable organisation,\"_ says <PERSON><PERSON>, \nMIB’s Director and Chair of the Selection Committee.\n\n_\"The Metropolitan Institute's mission is to plan for a healthier, more beautiful and more functional city. And it does this in collaboration with all the people at City Hall, and with all those who work for the city at different levels. I am also aware that fulfilling the vision of the kind of city we want is something that requires a lot of constructive solutions based on social consensus. I therefore accept my upcoming role with great responsibility to lead the conceptual work of the MIB in a transparent and open manner. I look forward to involving the city districts, the civic and private sector and especially the residents of Bratislava in the development of the city to improve the quality of life and public space. I was born and raised in Bratislava, and I have worked in urban and city planning in London and other cities for a long time. Bratislava has enormous potential, and it is a great privilege for me to have the opportunity to capitalise on my experience and actively contribute to and continue its positive transformation,\"_ says <PERSON>, MIB's newly selected Director.\n\n**The selection process**\n\nFive candidates applied for the vacancy. The Commission recommended four candidates to proceed to the next round of the selection process, one of whom withdrew. \n\nOne female and two male candidates presented their visions to the Committee at the [public hearing](https://www.youtube.com/watch?v=eihKvG_wD0s). The five-member Committee was composed of Ján Maz<PERSON>r, Director of the Metropolitan Institute of Bratislava (Chair of the Committee), Juraj Šujan, Bratislava's chief architect, Zuzana Vrbová from the LEAF organisation, Martina Slabejová, Director of the American Chamber of Commerce in Slovakia (AmCham Slovakia), and Branislav Záhradník, Bratislava City Councillor.\n\nThe decision was made following a public hearing held on 27 November 2023 at the Primate's Palace. The Committee regarded all three visions as being of high quality, but unanimously agreed to choose Petra Marko to fill the position of MIB's Director. The Committee also recommended Martin Zaicek for second place.\n\n**All the details of the selection process, including the CVs of the candidates before the Committee and the minutes of the selection process, can be found on the City Hall's website at Transparent** [Selection Policy](https://bratislava.sk/zivotne-prostredie-a-vystavba/rozvoj-mesta/metropolitny-institut-bratislavy).\n\n**The selected candidate was approved by the City Council Chamber of Deputies**\n\nThe City Council voted on 14 December 2023 to approve the commission's recommendation. Petra Marko will take over as head of the MIB in mid-January.\n**\nWho is Petra Marko?**\n\nPetra Marko is an architect and urban planning expert with many years of experience in public spaces and urban development. She has worked for many years in London, where she co-founded an urban design and placemaking studio involved in urban regeneration projects in the UK, Slovakia and the Czech Republic. She also has experience in project management and the development process of innovative residential projects in London. She has been part of the UK National Infrastructure Commission's Young Professionals Panel, which advises the government on long-term infrastructure development and, as a member of the Westminster Council Design Review Panel, she assesses projects that will shape central London. At the London School of Architecture, she has led research think-tanks on inclusive cities and sustainable mobility and has also worked at the Faculty of Architecture at the University of Applied Sciences in Bratislava. She co-authored the award-winning VeloCity strategic vision for sustainable rural growth and Meanwhile City, a handbook on the role of temporary interventions in the creation of habitable cities. For the last two years she has worked in Bratislava as a consultant and facilitator on the topics of green mobility, sustainable neighbourhood development and activation of public spaces. She regularly holds talks at international forums and writes for professional publications. Born and raised in Bratislava, she studied architecture in Vienna and Stockholm and completed postgraduate studies in creative entrepreneurship in London.", "files": [], "gallery": {"data": []}}}, "_nextI18Next": {"initialI18nStore": {"en": {"translation": {"AlertBanner.aria.closeAlert": "Close alert", "AliasInfoMessage.message.article": "You can also find this article at", "AliasInfoMessage.message.page": "You can also find this page at", "ArticlesFilter.allArticles": "All news", "ArticlesFilter.articleFilter": "News filter", "ArticlesFilter.subcategories": "Subcategories", "Brand.ariaLabel": "Home", "Brand.capitalCity": "<semibold>Bratislava</semibold> the Capital City of Slovakia", "Breadcrumbs.back": "Back", "Breadcrumbs.homepage": "Homepage", "Carousel.aria.controlButtons": "Control buttons", "Carousel.aria.next": "Next", "Carousel.aria.previous": "Previous", "common.download": "Download", "common.show": "Show", "common.showMore": "Show more", "ContactCtaCard.address": "Address", "ContactCtaCard.email": "Email", "ContactCtaCard.openingHours": "Office hours", "ContactCtaCard.phone": "Phone", "ContactCtaCard.web": "Web", "CopyToClipboardButton.copyToClipboard": "Copy to clipboard", "Dialog.aria.close": "Close dialog", "DocumentPageContent.descriptionTitle": "Description", "DocumentPageContent.detailsTitle": "Details", "DocumentPageContent.documentCategory": "Category", "DocumentPageContent.downloadButtonLabel": "Download file", "DocumentPageContent.numberOfFiles_one": "{{count}} file", "DocumentPageContent.numberOfFiles_other": "{{count}} files", "DocumentPageContent.publishedAt": "Created", "DocumentPageContent.updatedAt": "Updated", "DocumentsSection.openDocumentPage": "Go to page of document {{title}}", "EventCard.from": "from", "FileList.aria.downloadFile": "Download file", "FileList.aria.downloadFileAriaLabel": "Download file {{title}}, format {{format}}, {{size}}", "Footer.copyright": "© {{year}} <PERSON><PERSON><PERSON><PERSON> mesto <PERSON> Bratislava", "Gallery.aria.closeGallery": "Close image gallery", "Gallery.aria.descriptionSlider": "Slide<PERSON>", "Gallery.aria.imageLightBoxDescription": "Image gallery. Press left arrow key to go to previous image. Press right arrow key to go to next image. Press Escape key to close image gallery.", "Gallery.aria.nextImage": "Next image", "Gallery.aria.openGallery": "Open image gallery", "Gallery.aria.previousImage": "Previous image", "Gallery.morePhotos_one": "photo", "Gallery.morePhotos_other": "photos", "HomepageSearchResults.allResults": "All results", "HomepageSearchResults.sorryNoResultsFound": "Sorry, we couldn't find what you are looking for.", "HomepageTabs.allInformationOnSite": "All news can be find on site", "HomepageTabs.aria.tabListName": "Actual information", "HomepageTabs.tabs.Disclosure": "Public information", "HomepageTabs.tabs.LatestNews": "Latest News", "HomepageTabs.tabs.OfficialBoard": "Official noticeboard", "HomepageTabs.tabs.RoadClosures": "Road closures", "InbaArticle.publishedInThisRelease": "This article was published in the issue {{releaseTitle}} on {{releaseDate}}.", "InbaArticlesFilter.articleFilter": "Articles filter", "InbaRelease.inThisRelease": "In this issue", "InbaRelease.releasedOn": "Published on {{date}}", "InbaRelease.toDownload": "Downloads", "links.searchLink": "/search", "MinimumCalculator.adultsText": "Number of adults in your household", "MinimumCalculator.answerDescriptionNo": "To meet the subsistence level, you need to achieve an income of at least XY €. Therefore, you are not entitled to apply for temporary accommodation in our hostel.", "MinimumCalculator.answerDescriptionYes": "Your monthly income reaches the required amount and you are entitled to apply for temporary accommodation in our hostel.", "MinimumCalculator.answerNo": "No", "MinimumCalculator.answerYes": "Yes", "MinimumCalculator.buttonText": "Do I meet the subsistence level?", "MinimumCalculator.childrenText": "Number of children in your household", "MinimumCalculator.description": "Check quickly and easily whether your household's income is enough to cover the subsistence level. Without it, we cannot provide you with a place in our hostel.", "MinimumCalculator.incomeText": "The amount of monthly income of your household in €", "MinimumCalculator.placeholder": "Type a value", "MinimumCalculator.title": "Living wage calculator", "MobileNavBar.closeMenu": "Close the menu", "MobileNavBar.openMenu": "Open menu", "NavMenu.aria.backTo": "Back to {{backTo}}", "NavMenu.aria.navMenuLabel": "Main menu", "NavMenu.more": "More", "NotFound.sorryNoResultsFound": "Sorry, we couldn't find what you are looking for.", "NotFound.toTheMainPage": "Return to the home page.", "OfficialBoard.allOptions": "All", "OfficialBoard.archived": "Archived", "OfficialBoard.attachments": "Attachments", "OfficialBoard.category": "Category", "OfficialBoard.description": "Description", "OfficialBoard.details": "Details", "OfficialBoard.noAttachmentsMessage": "We didn't find any attachments to this notice.", "OfficialBoard.officialBoard": "Official noticeboard", "OfficialBoard.publicationState": "Publication state", "OfficialBoard.published": "Published", "OfficialBoard.publishedFrom": "Published from", "OfficialBoard.publishedTo": "Published to", "Pagination.aria.goToNextPage": "Go to next page", "Pagination.aria.goToPage": "Go to page {{page}}", "Pagination.aria.goToPreviousPage": "Go to previous page", "readMore": "Read more", "Regulation.amendment": "Amendment", "Regulation.amendments": "Amendments", "Regulation.attachments": "Attachments", "Regulation.cancelled": "Canceled", "Regulation.category.archiv": "Archive", "Regulation.category.daneAPoplatky": "Tax and fees", "Regulation.category.hospodarenie": "Economy", "Regulation.category.ostatne": "Other", "Regulation.category.pomenovanieUlic": "Street naming", "Regulation.category.poriadokACistota": "Order and cleanliness", "Regulation.category.socialnaPomocASkolstvo": "Social services and Education", "Regulation.category.uzemnePlanovanie": "Zoning", "Regulation.fullTextRegulation": "Full text regulation", "Regulation.influenceOnOtherRegulations": "Influence on other regulations", "Regulation.mainDocument": "Main document", "Regulation.noAmendmentsMessage": "There are no amendments to this regulation.", "Regulation.noAttachmentsMessage": "This regulation has no attachments.", "Regulation.numberOfAmendments_one": "{{count}} amendment", "Regulation.numberOfAmendments_other": "{{count}} amendments", "Regulation.numberOfAttachments_one": "{{count}} attachment", "Regulation.numberOfAttachments_other": "{{count}} attachments", "Regulation.regulations": "Generally binding regulations", "Regulation.relatedRegulations": "Related generally binding regulations", "Regulation.thisRegulationAmends": "This regulation amends", "Regulation.thisRegulationCancells": "This regulation cancels", "Regulation.thisRegulationDoesntAmend": "This regulation doesn't amend any regulations.", "Regulation.thisRegulationDoesntCancell": "This regulation doesn't cancel any regulation.", "Regulation.validity.cancelled": "Cancelled", "Regulation.validity.since": "since", "Regulation.validity.valid": "<PERSON><PERSON>", "RelatedArticlesSection.relatedArticles": "Related articles", "ScrollToTopButton.ariaLabel": "Scroll to top of the page", "SearchBar.search": "Search", "SearchPage.allResults": "All results", "SearchPage.article": "Article", "SearchPage.articles": "Articles", "SearchPage.contact": "Contact", "SearchPage.contacts": "Contacts", "SearchPage.document": "Document", "SearchPage.enterKeyword": "Enter a keyword", "SearchPage.enterSearchQuery": "Enter a search query", "SearchPage.inbaArticle": "in.ba article", "SearchPage.inbaArticles": "in.ba articles", "SearchPage.moreResults": "More results", "SearchPage.noResults": "No results found", "SearchPage.numberOfFiles_one": "{{count}} file", "SearchPage.numberOfFiles_other": "{{count}} files", "SearchPage.officialBoard": "Official board", "SearchPage.page": "Page", "SearchPage.pages": "Pages", "SearchPage.regulation": "Regulation", "SearchPage.regulations": "Regulations", "SearchPage.searching": "Search", "SearchPage.searchOptions": "Search options", "SearchPage.showingResults_one": "Showing {{count}} result", "SearchPage.showingResults_other": "Showing {{count}} results", "SearchPage.whatAreYouLookingFor": "What are you looking for?", "ShareButtonsSection.share": "Share", "SkipToContentButton.skipNavigation": "Skip navigation", "slider.aria.controlButtons": "Control buttons", "slider.aria.goToSlide": "Go to slide {{number}}", "TabPanelOfficialBoard.errorNoData": "Error while fetching data from the official board.", "Tag.aria.removeTag": "Remove tag {{tag}}", "TopServices.learnMore": "Read more", "useLocalizations.longName.en": "English", "useLocalizations.longName.sk": "<PERSON><PERSON><PERSON>", "useLocalizations.shortName.en": "EN", "useLocalizations.shortName.sk": "SK", "useTitle.title": "Bratislava.sk", "useTitle.titleWithChild": "{{childTitle}} – Bratislava.sk"}}, "sk": {"translation": {"AlertBanner.aria.closeAlert": "Zavrieť upozornenie", "AliasInfoMessage.message.article": "Tento článok nájdete aj na", "AliasInfoMessage.message.page": "<PERSON><PERSON><PERSON> str<PERSON> nájdete aj na", "ArticlesFilter.allArticles": "Všetky správy", "ArticlesFilter.articleFilter": "<PERSON><PERSON> spr<PERSON>v", "ArticlesFilter.subcategories": "Podkategórie", "Brand.ariaLabel": "<PERSON><PERSON>", "Brand.capitalCity": "<PERSON><PERSON><PERSON><PERSON> mesto SR <semibold>Bratislava</semibold>", "Breadcrumbs.back": "Späť", "Breadcrumbs.homepage": "<PERSON><PERSON>", "Carousel.aria.controlButtons": "Ov<PERSON><PERSON><PERSON><PERSON>č<PERSON>l<PERSON>", "Carousel.aria.next": "Ďalší", "Carousel.aria.previous": "Pre<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "common.download": "Stiahnuť", "common.show": "Zobraziť", "common.showMore": "Zobraziť viac", "ContactCtaCard.address": "<PERSON><PERSON><PERSON>", "ContactCtaCard.email": "Email", "ContactCtaCard.openingHours": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>", "ContactCtaCard.phone": "Telefón", "ContactCtaCard.web": "Web", "CopyToClipboardButton.copyToClipboard": "Kopírovať obsah", "Dialog.aria.close": "Zavrieť vyskakovacie okno", "DocumentPageContent.descriptionTitle": "<PERSON><PERSON>", "DocumentPageContent.detailsTitle": "Detaily", "DocumentPageContent.documentCategory": "Kategória", "DocumentPageContent.downloadButtonLabel": "Stiahnuť súbor", "DocumentPageContent.numberOfFiles_one": "{{count}} s<PERSON><PERSON>", "DocumentPageContent.numberOfFiles_few": "{{count}} s<PERSON><PERSON><PERSON>", "DocumentPageContent.numberOfFiles_many": "{{count}} s<PERSON><PERSON><PERSON>", "DocumentPageContent.numberOfFiles_other": "{{count}} s<PERSON><PERSON>v", "DocumentPageContent.publishedAt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DocumentPageContent.updatedAt": "Aktualizované", "DocumentsSection.openDocumentPage": "Prejsť na stránku dokumentu {{title}}", "EventCard.from": "od", "FileList.aria.downloadFile": "Stiahnuť súbor", "FileList.aria.downloadFileAriaLabel": "<PERSON><PERSON><PERSON><PERSON> súbor {{title}}, form<PERSON>t {{format}}, {{size}}", "Footer.copyright": "© {{year}} <PERSON><PERSON><PERSON><PERSON> mesto <PERSON> Bratislava, vyt<PERSON><PERSON> <innovations>Inovácie mesta Bratislava</innovations>", "Gallery.aria.closeGallery": "Zavrieť galériu o<PERSON>", "Gallery.aria.descriptionSlider": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gallery.aria.imageLightBoxDescription": "Galéria obrázkov. Stlačte šípku vľavo pre zobrazenie predchádzajúceho obrázku. Stlačte šípku vpravo pre zobrazenie nasledujúceho obrázku. Zatvorte stlačením klávesu Escape.", "Gallery.aria.nextImage": "Ďalší obrázok", "Gallery.aria.openGallery": "Otvoriť galériu o<PERSON>", "Gallery.aria.previousImage": "Predchádzajúci obrázok", "Gallery.morePhotos_one": "fotografia", "Gallery.morePhotos_few": "fotografie", "Gallery.morePhotos_many": "fotografií", "Gallery.morePhotos_other": "fotografií", "HomepageSearchResults.allResults": "Všetky výsledky", "HomepageSearchResults.sorryNoResultsFound": "<PERSON><PERSON><PERSON><PERSON><PERSON>, pre dané v<PERSON>ľadávanie sa nenašli žiadne výsledky.", "HomepageTabs.allInformationOnSite": "Všetky informácie nájdete na stránke", "HomepageTabs.aria.tabListName": "Aktuálne informácie", "HomepageTabs.tabs.Disclosure": "Zverejňovanie", "HomepageTabs.tabs.LatestNews": "Aktuality", "HomepageTabs.tabs.OfficialBoard": "Úradná tabuľa", "HomepageTabs.tabs.RoadClosures": "Rozkopávky", "InbaArticle.publishedInThisRelease": "Publikované v čísle {{releaseTitle}}, ktor<PERSON> v<PERSON> {{releaseDate}}.", "InbaArticlesFilter.articleFilter": "<PERSON><PERSON>", "InbaRelease.inThisRelease": "V tomto č<PERSON>le nájdete", "InbaRelease.releasedOn": "Publikované {{date}}", "InbaRelease.toDownload": "<PERSON> stia<PERSON>e", "links.searchLink": "/vyhladavanie", "MinimumCalculator.adultsText": "Počet dospelých vo Vašej domácnosti", "MinimumCalculator.answerDescriptionNo": "Na splnenie životného minima potrebujete dosiahnuť výšku príjmov aspoň XY €. Preto nemáte nárok uchádzať sa o prechodné ubytovanie v našej ubytovni.", "MinimumCalculator.answerDescriptionYes": "Vaše mesačné príjmy dosahujú potrebnú výšku a máte nárok uchádzať sa o prechodné ubytovanie v našej ubytovni.", "MinimumCalculator.answerNo": "<PERSON><PERSON>", "MinimumCalculator.answerYes": "Á<PERSON>", "MinimumCalculator.buttonText": "Spĺňam životné minimum?", "MinimumCalculator.childrenText": "Počet detí vo Vašej domácnosti", "MinimumCalculator.description": "Overte si rýchlo a j<PERSON>, či príjmy Vašej domácnosti stačia na pokrytie životného minima. Bez toho vám nevieme poskytnúť miesto v našej ubytovni.", "MinimumCalculator.incomeText": "Výška mesačných príjmov Vašej domácnosti v €", "MinimumCalculator.placeholder": "<PERSON><PERSON><PERSON><PERSON> hodnotu", "MinimumCalculator.title": "Kalkulačka na životné minimum", "MobileNavBar.closeMenu": "Zavrieť menu", "MobileNavBar.openMenu": "Otvor menu", "NavMenu.aria.backTo": "Späť na {{backTo}}", "NavMenu.aria.navMenuLabel": "Navigačné menu", "NavMenu.more": "Ďalšie", "NotFound.sorryNoResultsFound": "<PERSON><PERSON><PERSON><PERSON><PERSON>, zada<PERSON>ú stránku sme nenašli", "NotFound.toTheMainPage": "Na hlavnú stránku", "OfficialBoard.allOptions": "<PERSON><PERSON><PERSON><PERSON>", "OfficialBoard.archived": "Archivované", "OfficialBoard.attachments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OfficialBoard.category": "Kategória", "OfficialBoard.description": "<PERSON><PERSON>", "OfficialBoard.details": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OfficialBoard.noAttachmentsMessage": "K tomuto dokumentu sa nepodarilo nájsť žiadne prílohy.", "OfficialBoard.officialBoard": "Úradná tabuľa", "OfficialBoard.publicationState": "<PERSON><PERSON>", "OfficialBoard.published": "Zverejn<PERSON><PERSON>", "OfficialBoard.publishedFrom": "Zverejnené od", "OfficialBoard.publishedTo": "Zverejnené do", "Pagination.aria.goToNextPage": "Ísť na ďalšiu stranu", "Pagination.aria.goToPage": "Ísť na stranu {{page}}", "Pagination.aria.goToPreviousPage": "Ísť na predchádzajúcu stranu", "readMore": "Čítať viac", "Regulation.amendment": "Doplňujúce nariadenie", "Regulation.amendments": "<PERSON><PERSON><PERSON><PERSON>", "Regulation.attachments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Regulation.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Regulation.category.archiv": "Archív", "Regulation.category.daneAPoplatky": "<PERSON> a poplatky", "Regulation.category.hospodarenie": "Ho<PERSON>od<PERSON><PERSON><PERSON>", "Regulation.category.ostatne": "Ostatné", "Regulation.category.pomenovanieUlic": "Po<PERSON><PERSON><PERSON>", "Regulation.category.poriadokACistota": "Poriadok a čistota", "Regulation.category.socialnaPomocASkolstvo": "Sociálna pomoc a školstvo", "Regulation.category.uzemnePlanovanie": "Územné p<PERSON>", "Regulation.fullTextRegulation": "Úplné znenie", "Regulation.influenceOnOtherRegulations": "Vplyv na iné VZN", "Regulation.mainDocument": "Hlavný dokument", "Regulation.noAmendmentsMessage": "K tomuto VZN neexistuj<PERSON>.", "Regulation.noAttachmentsMessage": "Toto VZN nemá prílohy.", "Regulation.numberOfAmendments_one": "{{count}} dodatok", "Regulation.numberOfAmendments_few": "{{count}} do<PERSON><PERSON><PERSON>", "Regulation.numberOfAmendments_many": "{{count}} do<PERSON><PERSON><PERSON>", "Regulation.numberOfAmendments_other": "{{count}} do<PERSON><PERSON><PERSON>", "Regulation.numberOfAttachments_one": "{{count}} p<PERSON><PERSON><PERSON><PERSON>", "Regulation.numberOfAttachments_few": "{{count}} p<PERSON><PERSON><PERSON><PERSON>", "Regulation.numberOfAttachments_many": "{{count}} pr<PERSON><PERSON>h", "Regulation.numberOfAttachments_other": "{{count}} pr<PERSON><PERSON>h", "Regulation.regulations": "Všeobecne záväzné nariadenia", "Regulation.relatedRegulations": "Súvisiace všeobecne záväzné nariadenia", "Regulation.thisRegulationAmends": "Toto VZN je dodatkom k", "Regulation.thisRegulationCancells": "Toto VZN zrušuje", "Regulation.thisRegulationDoesntAmend": "Toto VZN nie je dodatkom k žiadnemu VZN.", "Regulation.thisRegulationDoesntCancell": "Toto VZN nezrušuje žiadne VZN.", "Regulation.validity.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Regulation.validity.since": "od", "Regulation.validity.valid": "<PERSON><PERSON><PERSON><PERSON>", "RelatedArticlesSection.relatedArticles": "Súvisiace články", "ScrollToTopButton.ariaLabel": "Vrátiť sa na začiatok stránky", "SearchBar.search": "Hľadať", "SearchPage.allResults": "Všetky výsledky", "SearchPage.article": "Článok", "SearchPage.articles": "<PERSON><PERSON><PERSON><PERSON>", "SearchPage.contact": "Kontakt", "SearchPage.contacts": "Kontakty", "SearchPage.document": "Dokument", "SearchPage.enterKeyword": "Zadajte kľúčové slovo", "SearchPage.enterSearchQuery": "Zadajte hľadaný výraz", "SearchPage.inbaArticle": "in.ba <PERSON><PERSON><PERSON>", "SearchPage.inbaArticles": "in.ba <PERSON><PERSON><PERSON>", "SearchPage.moreResults": "Viac výsledkov", "SearchPage.noResults": "Žiadne výsledky", "SearchPage.numberOfFiles_one": "{{count}} s<PERSON><PERSON>", "SearchPage.numberOfFiles_few": "{{count}} s<PERSON><PERSON><PERSON>", "SearchPage.numberOfFiles_many": "{{count}} s<PERSON><PERSON>v", "SearchPage.numberOfFiles_other": "{{count}} s<PERSON><PERSON>v", "SearchPage.officialBoard": "Úradná tabuľa", "SearchPage.page": "Strán<PERSON>", "SearchPage.pages": "<PERSON><PERSON><PERSON><PERSON>", "SearchPage.regulation": "VZN", "SearchPage.regulations": "VZN", "SearchPage.searching": "Vyhľadávanie", "SearchPage.searchOptions": "Možnosti vyhľadávania", "SearchPage.showingResults_one": "Zobrazujeme {{count}} výsledok", "SearchPage.showingResults_few": "Zobrazujeme {{count}} v<PERSON><PERSON><PERSON><PERSON>", "SearchPage.showingResults_many": "Zobrazujeme {{count}} v<PERSON><PERSON><PERSON><PERSON>", "SearchPage.showingResults_other": "Zobrazujeme {{count}} v<PERSON><PERSON><PERSON><PERSON>", "SearchPage.whatAreYouLookingFor": "<PERSON>o <PERSON>?", "ShareButtonsSection.share": "Zdieľať", "SkipToContentButton.skipNavigation": "Preskočiť navigáciu", "slider.aria.controlButtons": "Ov<PERSON><PERSON><PERSON><PERSON>č<PERSON>l<PERSON>", "slider.aria.goToSlide": "Ísť na slide {{number}}", "TabPanelOfficialBoard.errorNoData": "Nepodarilo sa načítať dáta z úradnej tabule.", "Tag.aria.removeTag": "Odstrániť tag {{tag}}", "TopServices.learnMore": "Zistiť viac", "useLocalizations.longName.en": "English", "useLocalizations.longName.sk": "<PERSON><PERSON><PERSON>", "useLocalizations.shortName.en": "EN", "useLocalizations.shortName.sk": "SK", "useTitle.title": "Bratislava.sk", "useTitle.titleWithChild": "{{childTitle}} – Bratislava.sk"}}}, "initialLocale": "en", "ns": ["translation"], "userConfig": {"i18n": {"defaultLocale": "sk", "locales": ["sk", "en"], "localeDetection": false}, "defaultNS": ["translation"], "reloadOnPrerender": false, "default": {"i18n": {"defaultLocale": "sk", "locales": ["sk", "en"], "localeDetection": false}, "defaultNS": ["translation"], "reloadOnPrerender": false}}}}, "__N_SSG": true}