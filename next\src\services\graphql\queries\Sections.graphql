fragment IconTitleDescriptionBlock on ComponentBlocksIconWithTitleAndDescription {
  title
  desc
  disableIconBackground
  icon {
    data {
      ...UploadImageSrcEntity
    }
  }
}

fragment TimelineItemBlock on ComponentBlocksTimelineItem {
  id
  title
  content
}

fragment GallerySection on ComponentSectionsGallery {
  title
  text
  medias(pagination: { limit: -1 }) {
    data {
      ...UploadImageEntity
    }
  }
}

fragment TimelineSection on ComponentSectionsTimeline {
  timelineItems {
    ...TimelineItemBlock
  }
}

fragment ArticlesSection on ComponentSectionsArticles {
  title
  text
  showAll
  category {
    data {
      ...PageCategoryEntity
    }
  }
}

fragment InbaArticlesListSection on ComponentSectionsInbaArticlesList {
  title
  text
}

fragment InbaReleasesSection on ComponentSectionsInbaReleases {
  title
  text
}

fragment IconTitleDescSection on ComponentSectionsIconTitleDesc {
  title
  list {
    ...IconTitleDescriptionBlock
  }
}

fragment DividerSection on ComponentSectionsDivider {
  style
}

fragment TextWithImageSection on ComponentSectionsTextWithImage {
  content
  imagePosition
  imageSrc {
    data {
      ...UploadImageEntity
    }
  }
  imageAspectRatio
  links {
    ...CommonLink
  }
}

fragment TextWithImageOverlappedSection on ComponentSectionsTextWithImageOverlapped {
  content
  imagePositionTextWithImageOverlapped: imagePosition
  image {
    data {
      ...UploadImageEntity
    }
  }
  links {
    ...CommonLink
  }
}

fragment IframeSection on ComponentSectionsIframe {
  url
  iframeWidth
  iframeHeight
  fullHeight
  allowFullscreen
  css
  allowGeolocation
}

fragment FileBlock on ComponentBlocksFile {
  id
  title
  media {
    data {
      ...UploadFileEntity
    }
  }
}

fragment FileListSection on ComponentSectionsFileList {
  title
  text
  fileList(pagination: { limit: -1 }) {
    ...FileBlock
  }
}

fragment FileItemBlock on ComponentBlocksFileItem {
  id
  title
  media {
    data {
      ...UploadFileEntity
    }
  }
}

fragment ColumnedTextSection on ComponentSectionsColumnedText {
  title
  content
}

fragment ColumnsItem on ComponentBlocksColumnsItem {
  title
  text
  image {
    data {
      ...UploadImageEntity
    }
  }
}

fragment ColumnsSection on ComponentSectionsColumns {
  title
  text
  columns(pagination: { limit: -1 }) {
    ...ColumnsItem
  }
  imageVariant
  responsiveLayout
}

fragment NarrowTextSection on ComponentSectionsNarrowText {
  content
  width
  align
}

fragment LinksSection on ComponentSectionsLinks {
  title
  pageLinks(pagination: { limit: -1 }) {
    ...PageLink
  }
}

fragment ComponentAccordionItemsInstitutionNarrow on ComponentAccordionItemsInstitutionNarrow {
  title
  subtitle
  category
  url
  urlLabel
}

fragment ComponentAccordionItemsFlatText on ComponentAccordionItemsFlatText {
  category
  content
  width
  align
  moreLinkTitle
  moreLinkUrl
  moreLinkPage {
    data {
      attributes {
        slug
        title
        locale
      }
    }
  }
  fileList {
    ...FileItemBlock
  }
}

fragment ComponentAccordionItemsInstitution on ComponentAccordionItemsInstitution {
  title
  subtitle
  category
  firstColumn
  secondColumn
  thirdColumn
  url
  urlLabel
}

fragment AccordionSection on ComponentSectionsAccordion {
  title
  institutions(pagination: { limit: -1 }) {
    ...ComponentAccordionItemsInstitution
  }
  flatText(pagination: { limit: -1 }) {
    ...ComponentAccordionItemsFlatText
  }
  institutionsNarrow(pagination: { limit: -1 }) {
    ...ComponentAccordionItemsInstitutionNarrow
  }
}

fragment CalculatorSection on ComponentSectionsCalculator {
  single_adult_value
  another_adult_value
  child_value
}

fragment VideoBlock on ComponentBlocksVideo {
  id
  title
  speaker
  url
}

fragment VideosSection on ComponentSectionsVideos {
  id
  title
  subtitle
  videos {
    ...VideoBlock
  }
}

fragment NumericalListItemBlock on ComponentBlocksNumericalListItem {
  text
}

fragment NumericalListSection on ComponentSectionsNumericalList {
  id
  items {
    ...NumericalListItemBlock
  }
  title
  variant
  buttonText
  buttonLink
}

fragment OrganizationalStructureSection on ComponentSectionsOrganizationalStructure {
  title
}

fragment ProsAndConsCardComponent on ComponentBlocksProsAndConsCard {
  title
  items {
    label
  }
}

fragment ProsAndConsSection on ComponentSectionsProsAndConsSection {
  title
  text
  textAlignProsAndCons: textAlign
  #  showMoreLink {
  #    ...CommonLink
  #  }
  pros {
    ...ProsAndConsCardComponent
  }
  cons {
    ...ProsAndConsCardComponent
  }
}

fragment ComparisonCardComponent on ComponentBlocksComparisonCard {
  title
  items {
    label
  }
  iconMedia {
    data {
      attributes {
        url
      }
    }
  }
}

fragment ComparisonSection on ComponentSectionsComparisonSection {
  title
  text
  textAlignComparison: textAlign
  #  showMoreLink {
  #    ...CommonLink
  #  }
  cards {
    ...ComparisonCardComponent
  }
}

fragment BannerSection on ComponentSectionsBanner {
  bannerTitle: title
  content
  contentPosition
  bannerVariant: variant
  media {
    data {
      attributes {
        url
      }
    }
  }
  primaryLink {
    ...CommonLink
  }
  secondaryLink {
    ...CommonLink
  }
  tertiaryLink {
    ...CommonLink
  }
}

fragment ContactCardBlock on ComponentBlocksContactCard {
  overrideLabel
  value
}

fragment ContactPersonCardBlock on ComponentBlocksContactPersonCard {
  title
  subtext
  email
  phone
}

fragment ContactsSection on ComponentSectionsContactsSection {
  id
  title
  description
  addressContacts {
    ...ContactCardBlock
  }
  openingHoursContacts {
    ...ContactCardBlock
  }
  emailContacts {
    ...ContactCardBlock
  }
  phoneContacts {
    ...ContactCardBlock
  }
  webContacts {
    ...ContactCardBlock
  }
  personContacts {
    ...ContactPersonCardBlock
  }
}

fragment RegulationsSection on ComponentSectionsRegulations {
  regulations {
    data {
      ...RegulationEntity
    }
  }
}

fragment FaqsSection on ComponentSectionsFaqs {
  title
  text
  faqs {
    data {
      ...FaqEntity
    }
  }
}

fragment FaqCategoriesSection on ComponentSectionsFaqCategories {
  id
  title
  text
  faqCategories {
    data {
      ...FaqCategoryEntity
    }
  }
}

fragment TootootEventsSection on ComponentSectionsTootootEvents {
  title
  text
  showMoreLink {
    ...CommonLink
  }
}

fragment PartnerBlock on ComponentBlocksPartner {
  title
  url
  logo {
    data {
      ...UploadImageEntity
    }
  }
}

fragment PartnersSection on ComponentSectionsPartners {
  title
  text
  partners(pagination: { limit: -1 }) {
    ...PartnerBlock
  }
  logoRatio
}

fragment DocumentsSection on ComponentSectionsDocuments {
  title
  text
  documents(pagination: { limit: -1 }) {
    data {
      ...DocumentEntity
    }
  }
}

fragment Sections on PageSectionsDynamicZone {
  __typename

  ... on ComponentSectionsIconTitleDesc {
    ...IconTitleDescSection
  }

  ... on ComponentSectionsDivider {
    ...DividerSection
  }

  ... on ComponentSectionsTextWithImage {
    ...TextWithImageSection
  }

  ... on ComponentSectionsTextWithImageOverlapped {
    ...TextWithImageOverlappedSection
  }

  ... on ComponentSectionsIframe {
    ...IframeSection
  }

  ... on ComponentSectionsGallery {
    ...GallerySection
  }

  ... on ComponentSectionsFileList {
    ...FileListSection
  }

  ... on ComponentSectionsColumnedText {
    ...ColumnedTextSection
  }

  ... on ComponentSectionsColumns {
    ...ColumnsSection
  }

  ... on ComponentSectionsNarrowText {
    ...NarrowTextSection
  }

  ... on ComponentSectionsLinks {
    ...LinksSection
  }

  ... on ComponentSectionsAccordion {
    ...AccordionSection
  }

  ... on ComponentSectionsCalculator {
    ...CalculatorSection
  }

  ... on ComponentSectionsVideos {
    ...VideosSection
  }

  ... on ComponentSectionsNumericalList {
    ...NumericalListSection
  }

  ... on ComponentSectionsArticles {
    ...ArticlesSection
  }

  ... on ComponentSectionsInbaArticlesList {
    ...InbaArticlesListSection
  }

  ... on ComponentSectionsInbaReleases {
    ...InbaReleasesSection
  }

  ... on ComponentSectionsOrganizationalStructure {
    ...OrganizationalStructureSection
  }

  # ComponentSectionsOfficialBoard has no attributes

  ... on ComponentSectionsProsAndConsSection {
    ...ProsAndConsSection
  }

  ... on ComponentSectionsComparisonSection {
    ...ComparisonSection
  }

  ... on ComponentSectionsBanner {
    ...BannerSection
  }

  ... on ComponentSectionsTimeline {
    ...TimelineSection
  }

  ... on ComponentSectionsContactsSection {
    ...ContactsSection
  }

  # ComponentSectionsRegulationsList has no attributes

  ... on ComponentSectionsRegulations {
    ...RegulationsSection
  }

  ... on ComponentSectionsFaqs {
    ...FaqsSection
  }

  ... on ComponentSectionsFaqCategories {
    ...FaqCategoriesSection
  }

  ... on ComponentSectionsTootootEvents {
    ...TootootEventsSection
  }

  ... on ComponentSectionsPartners {
    ...PartnersSection
  }

  ... on ComponentSectionsDocuments {
    ...DocumentsSection
  }
}

fragment SubpageListPageHeaderSection on ComponentSectionsSubpageList {
  id
  subpageList(pagination: { limit: -1 }) {
    ...PageLink
  }
}

fragment PageHeaderSections on PagePageHeaderSectionsDynamicZone {
  __typename

  ... on ComponentSectionsSubpageList {
    ...SubpageListPageHeaderSection
  }
}
