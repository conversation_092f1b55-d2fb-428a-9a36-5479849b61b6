{"kind": "collectionType", "collectionName": "documents", "info": {"singularName": "document", "pluralName": "documents", "displayName": "Dokumenty", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "title", "required": true}, "files": {"type": "media", "multiple": true, "required": true, "allowedTypes": ["images", "files"]}, "description": {"type": "text"}, "documentCategory": {"type": "relation", "relation": "manyToOne", "target": "api::document-category.document-category", "inversedBy": "documents"}}}