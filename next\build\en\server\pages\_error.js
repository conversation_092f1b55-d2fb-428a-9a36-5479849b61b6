/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5C_error.jsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5C_error.jsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_error_jsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\_error.jsx */ \"./src/pages/_error.jsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\nprivate_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_error_jsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_error_jsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_error_jsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_error_jsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_error_jsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_error_jsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_error_jsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_error_jsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_error_jsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_error_jsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_error_jsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_error_jsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5C_error.jsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/common/NavBar/NavMenu/navMenuContext.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/common/NavBar/NavMenu/navMenuContext.tsx ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavMenuContextProvider: () => (/* binding */ NavMenuContextProvider),\n/* harmony export */   useNavMenuContext: () => (/* binding */ useNavMenuContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst NavMenuContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    menuValue: \"\",\n    setMenuValue: ()=>{},\n    isMobileMenuOpen: false,\n    setMobileMenuOpen: ()=>{}\n});\nconst NavMenuContextProvider = ({ children })=>{\n    const [menuValue, setMenuValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isMobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavMenuContext.Provider, {\n        value: {\n            menuValue,\n            setMenuValue,\n            isMobileMenuOpen,\n            setMobileMenuOpen\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\components\\\\common\\\\NavBar\\\\NavMenu\\\\navMenuContext.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\nconst useNavMenuContext = ()=>{\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NavMenuContext);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/common/NavBar/NavMenu/navMenuContext.tsx\n");

/***/ }),

/***/ "./src/components/providers/BAI18nProvider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/BAI18nProvider.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_aria__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-aria */ \"react-aria\");\n/* harmony import */ var _src_utils_useLocale__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/utils/useLocale */ \"./src/utils/useLocale.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_aria__WEBPACK_IMPORTED_MODULE_2__]);\nreact_aria__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// Copied from: https://github.com/bratislava/marianum/blob/762d10222bd33352b77a44d902620181b07107c1/next/components/atoms/MI18nProvider.tsx\n// eslint-disable-next-line @typescript-eslint/ban-types\nconst BAI18nProvider = ({ children })=>{\n    const locale = (0,_src_utils_useLocale__WEBPACK_IMPORTED_MODULE_3__.useLocale)();\n    const reactAriaLocale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (locale === \"en\") {\n            /* https://unix.stackexchange.com/a/62317\n       * https://github.com/date-fns/date-fns/issues/1996#issuecomment-984811417 */ return \"en-IE\";\n        }\n        return \"sk-SK\";\n    }, [\n        locale\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_aria__WEBPACK_IMPORTED_MODULE_2__.I18nProvider, {\n        locale: reactAriaLocale,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\components\\\\providers\\\\BAI18nProvider.tsx\",\n        lineNumber: 22,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BAI18nProvider);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./src/components/providers/BAI18nProvider.tsx\n");

/***/ }),

/***/ "./src/components/providers/BAQueryClientProvider.tsx":
/*!************************************************************!*\
  !*** ./src/components/providers/BAQueryClientProvider.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"@tanstack/react-query\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__]);\n_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n/**\n * Docs: https://tanstack.com/query/latest/docs/framework/react/guides/ssr#initial-setup\n *\n * @param children\n * @constructor\n */ const BAQueryClientProvider = ({ children })=>{\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClient());\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClientProvider, {\n        client: queryClient,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\components\\\\providers\\\\BAQueryClientProvider.tsx\",\n        lineNumber: 13,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BAQueryClientProvider);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvQkFRdWVyeUNsaWVudFByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQXdFO0FBQ3JCO0FBRW5EOzs7OztDQUtDLEdBQ0QsTUFBTUcsd0JBQXdCLENBQUMsRUFBRUMsUUFBUSxFQUFxQjtJQUM1RCxNQUFNLENBQUNDLFlBQVksR0FBR0gsK0NBQVFBLENBQUMsSUFBTSxJQUFJRiw4REFBV0E7SUFFcEQscUJBQU8sOERBQUNDLHNFQUFtQkE7UUFBQ0ssUUFBUUQ7a0JBQWNEOzs7Ozs7QUFDcEQ7QUFFQSxpRUFBZUQscUJBQXFCQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnJhdGlzbGF2YS1uZXh0Ly4vc3JjL2NvbXBvbmVudHMvcHJvdmlkZXJzL0JBUXVlcnlDbGllbnRQcm92aWRlci50c3g/Mjg2NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBRdWVyeUNsaWVudCwgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSdcbmltcG9ydCB7IFByb3BzV2l0aENoaWxkcmVuLCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuXG4vKipcbiAqIERvY3M6IGh0dHBzOi8vdGFuc3RhY2suY29tL3F1ZXJ5L2xhdGVzdC9kb2NzL2ZyYW1ld29yay9yZWFjdC9ndWlkZXMvc3NyI2luaXRpYWwtc2V0dXBcbiAqXG4gKiBAcGFyYW0gY2hpbGRyZW5cbiAqIEBjb25zdHJ1Y3RvclxuICovXG5jb25zdCBCQVF1ZXJ5Q2xpZW50UHJvdmlkZXIgPSAoeyBjaGlsZHJlbiB9OiBQcm9wc1dpdGhDaGlsZHJlbikgPT4ge1xuICBjb25zdCBbcXVlcnlDbGllbnRdID0gdXNlU3RhdGUoKCkgPT4gbmV3IFF1ZXJ5Q2xpZW50KCkpXG5cbiAgcmV0dXJuIDxRdWVyeUNsaWVudFByb3ZpZGVyIGNsaWVudD17cXVlcnlDbGllbnR9PntjaGlsZHJlbn08L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG59XG5cbmV4cG9ydCBkZWZhdWx0IEJBUXVlcnlDbGllbnRQcm92aWRlclxuIl0sIm5hbWVzIjpbIlF1ZXJ5Q2xpZW50IiwiUXVlcnlDbGllbnRQcm92aWRlciIsInVzZVN0YXRlIiwiQkFRdWVyeUNsaWVudFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJxdWVyeUNsaWVudCIsImNsaWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/providers/BAQueryClientProvider.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_variable_inter_font_subsets_latin_latin_ext_variableName_inter___WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\pages\\\\_app.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--inter-font\",\"subsets\":[\"latin\",\"latin-ext\"]}],\"variableName\":\"inter\"} */ \"./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\pages\\\\\\\\_app.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--inter-font\\\",\\\"subsets\\\":[\\\"latin\\\",\\\"latin-ext\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_variable_inter_font_subsets_latin_latin_ext_variableName_inter___WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_variable_inter_font_subsets_latin_latin_ext_variableName_inter___WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_vertical_timeline_component_style_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-vertical-timeline-component/style.min.css */ \"./node_modules/react-vertical-timeline-component/style.min.css\");\n/* harmony import */ var react_vertical_timeline_component_style_min_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_vertical_timeline_component_style_min_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _src_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/src/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _src_styles_globals_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_src_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _next_third_parties_google__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @next/third-parties/google */ \"@next/third-parties/google\");\n/* harmony import */ var _next_third_parties_google__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_next_third_parties_google__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_plausible__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-plausible */ \"next-plausible\");\n/* harmony import */ var next_plausible__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_plausible__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_query_params__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-query-params */ \"next-query-params\");\n/* harmony import */ var next_query_params__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_query_params__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react_aria__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-aria */ \"react-aria\");\n/* harmony import */ var use_query_params__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! use-query-params */ \"use-query-params\");\n/* harmony import */ var use_query_params__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(use_query_params__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _src_components_common_NavBar_NavMenu_navMenuContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/src/components/common/NavBar/NavMenu/navMenuContext */ \"./src/components/common/NavBar/NavMenu/navMenuContext.tsx\");\n/* harmony import */ var _src_components_providers_BAI18nProvider__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/src/components/providers/BAI18nProvider */ \"./src/components/providers/BAI18nProvider.tsx\");\n/* harmony import */ var _src_components_providers_BAQueryClientProvider__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/src/components/providers/BAQueryClientProvider */ \"./src/components/providers/BAQueryClientProvider.tsx\");\n/* harmony import */ var _src_utils_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/src/utils/utils */ \"./src/utils/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_aria__WEBPACK_IMPORTED_MODULE_9__, _src_components_providers_BAI18nProvider__WEBPACK_IMPORTED_MODULE_12__, _src_components_providers_BAQueryClientProvider__WEBPACK_IMPORTED_MODULE_13__]);\n([react_aria__WEBPACK_IMPORTED_MODULE_9__, _src_components_providers_BAI18nProvider__WEBPACK_IMPORTED_MODULE_12__, _src_components_providers_BAQueryClientProvider__WEBPACK_IMPORTED_MODULE_13__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* eslint-disable @next/next/inline-script-id */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MyApp = ({ Component, pageProps })=>{\n    const isProd = (0,_src_utils_utils__WEBPACK_IMPORTED_MODULE_14__.isProductionDeployment)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_5___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"a074f0e4906de591\",\n                        dynamic: [\n                            (next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_variable_inter_font_subsets_latin_latin_ext_variableName_inter___WEBPACK_IMPORTED_MODULE_15___default().style).fontFamily\n                        ],\n                        children: `body{font-family:${(next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_variable_inter_font_subsets_latin_latin_ext_variableName_inter___WEBPACK_IMPORTED_MODULE_15___default().style).fontFamily}}`\n                    }, void 0, false, void 0, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\",\n                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                            [\n                                \"a074f0e4906de591\",\n                                [\n                                    (next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_variable_inter_font_subsets_latin_latin_ext_variableName_inter___WEBPACK_IMPORTED_MODULE_15___default().style).fontFamily\n                                ]\n                            ]\n                        ])\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\",\n                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                            [\n                                \"a074f0e4906de591\",\n                                [\n                                    (next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_variable_inter_font_subsets_latin_latin_ext_variableName_inter___WEBPACK_IMPORTED_MODULE_15___default().style).fontFamily\n                                ]\n                            ]\n                        ])\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\",\n                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                            [\n                                \"a074f0e4906de591\",\n                                [\n                                    (next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_variable_inter_font_subsets_latin_latin_ext_variableName_inter___WEBPACK_IMPORTED_MODULE_15___default().style).fontFamily\n                                ]\n                            ]\n                        ])\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\",\n                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                            [\n                                \"a074f0e4906de591\",\n                                [\n                                    (next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_variable_inter_font_subsets_latin_latin_ext_variableName_inter___WEBPACK_IMPORTED_MODULE_15___default().style).fontFamily\n                                ]\n                            ]\n                        ])\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"mask-icon\",\n                        href: \"/safari-pinned-tab.svg\",\n                        color: \"#e46054\",\n                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                            [\n                                \"a074f0e4906de591\",\n                                [\n                                    (next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_variable_inter_font_subsets_latin_latin_ext_variableName_inter___WEBPACK_IMPORTED_MODULE_15___default().style).fontFamily\n                                ]\n                            ]\n                        ])\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#da532c\",\n                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                            [\n                                \"a074f0e4906de591\",\n                                [\n                                    (next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_variable_inter_font_subsets_latin_latin_ext_variableName_inter___WEBPACK_IMPORTED_MODULE_15___default().style).fontFamily\n                                ]\n                            ]\n                        ])\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#ffffff\",\n                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                            [\n                                \"a074f0e4906de591\",\n                                [\n                                    (next_font_google_target_css_path_src_pages_app_tsx_import_Inter_arguments_variable_inter_font_subsets_latin_latin_ext_variableName_inter___WEBPACK_IMPORTED_MODULE_15___default().style).fontFamily\n                                ]\n                            ]\n                        ])\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_plausible__WEBPACK_IMPORTED_MODULE_7___default()), {\n                domain: isProd ? \"bratislava.sk\" : \"testing.bratislava.sk\",\n                taggedEvents: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_providers_BAQueryClientProvider__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(use_query_params__WEBPACK_IMPORTED_MODULE_10__.QueryParamProvider, {\n                        adapter: next_query_params__WEBPACK_IMPORTED_MODULE_8__.NextAdapter,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_providers_BAI18nProvider__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_aria__WEBPACK_IMPORTED_MODULE_9__.OverlayProvider, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_common_NavBar_NavMenu_navMenuContext__WEBPACK_IMPORTED_MODULE_11__.NavMenuContextProvider, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            id: \"root\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                                ...pageProps\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_next_third_parties_google__WEBPACK_IMPORTED_MODULE_4__.GoogleTagManager, {\n                                            gtmId: \"\" ?? 0,\n                                            auth: \"\" ?? 0,\n                                            preview: \"\" ?? 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_i18next__WEBPACK_IMPORTED_MODULE_6__.appWithTranslation)(MyApp));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/_error.jsx":
/*!******************************!*\
  !*** ./src/pages/_error.jsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/error */ \"next/error\");\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_error__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// eslint-disable-next-line react/prop-types\nconst MyError = ({ statusCode })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_error__WEBPACK_IMPORTED_MODULE_1___default()), {\n        statusCode: statusCode\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Repos\\\\bratislava.sk-1\\\\next\\\\src\\\\pages\\\\_error.jsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, undefined);\n};\nMyError.getInitialProps = async (context)=>{\n    const errorInitialProps = await next_error__WEBPACK_IMPORTED_MODULE_1___default().getInitialProps(context);\n    errorInitialProps.hasGetInitialPropsRun = true;\n    return errorInitialProps;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyError);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2Vycm9yLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMkM7QUFFM0MsNENBQTRDO0FBQzVDLE1BQU1DLFVBQVUsQ0FBQyxFQUFFQyxVQUFVLEVBQUU7SUFDN0IscUJBQU8sOERBQUNGLG1EQUFrQkE7UUFBQ0UsWUFBWUE7Ozs7OztBQUN6QztBQUVBRCxRQUFRRSxlQUFlLEdBQUcsT0FBT0M7SUFDL0IsTUFBTUMsb0JBQW9CLE1BQU1MLGlFQUFrQyxDQUFDSTtJQUVuRUMsa0JBQWtCQyxxQkFBcUIsR0FBRztJQUUxQyxPQUFPRDtBQUNUO0FBRUEsaUVBQWVKLE9BQU9BLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9icmF0aXNsYXZhLW5leHQvLi9zcmMvcGFnZXMvX2Vycm9yLmpzeD82M2JjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBOZXh0RXJyb3JDb21wb25lbnQgZnJvbSAnbmV4dC9lcnJvcidcclxuXHJcbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC9wcm9wLXR5cGVzXHJcbmNvbnN0IE15RXJyb3IgPSAoeyBzdGF0dXNDb2RlIH0pID0+IHtcclxuICByZXR1cm4gPE5leHRFcnJvckNvbXBvbmVudCBzdGF0dXNDb2RlPXtzdGF0dXNDb2RlfSAvPlxyXG59XHJcblxyXG5NeUVycm9yLmdldEluaXRpYWxQcm9wcyA9IGFzeW5jIChjb250ZXh0KSA9PiB7XHJcbiAgY29uc3QgZXJyb3JJbml0aWFsUHJvcHMgPSBhd2FpdCBOZXh0RXJyb3JDb21wb25lbnQuZ2V0SW5pdGlhbFByb3BzKGNvbnRleHQpXHJcblxyXG4gIGVycm9ySW5pdGlhbFByb3BzLmhhc0dldEluaXRpYWxQcm9wc1J1biA9IHRydWVcclxuXHJcbiAgcmV0dXJuIGVycm9ySW5pdGlhbFByb3BzXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IE15RXJyb3JcclxuIl0sIm5hbWVzIjpbIk5leHRFcnJvckNvbXBvbmVudCIsIk15RXJyb3IiLCJzdGF0dXNDb2RlIiwiZ2V0SW5pdGlhbFByb3BzIiwiY29udGV4dCIsImVycm9ySW5pdGlhbFByb3BzIiwiaGFzR2V0SW5pdGlhbFByb3BzUnVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_error.jsx\n");

/***/ }),

/***/ "./src/utils/useLocale.ts":
/*!********************************!*\
  !*** ./src/utils/useLocale.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLocale: () => (/* binding */ useLocale)\n/* harmony export */ });\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useLocale = ()=>{\n    const { i18n } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_0__.useTranslation)();\n    return i18n.language;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvdXRpbHMvdXNlTG9jYWxlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUV0QyxNQUFNQyxZQUFZO0lBQ3ZCLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUdGLDREQUFjQTtJQUUvQixPQUFPRSxLQUFLQyxRQUFRO0FBQ3RCLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9icmF0aXNsYXZhLW5leHQvLi9zcmMvdXRpbHMvdXNlTG9jYWxlLnRzPzJkZTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tICduZXh0LWkxOG5leHQnXG5cbmV4cG9ydCBjb25zdCB1c2VMb2NhbGUgPSAoKSA9PiB7XG4gIGNvbnN0IHsgaTE4biB9ID0gdXNlVHJhbnNsYXRpb24oKVxuXG4gIHJldHVybiBpMThuLmxhbmd1YWdlXG59XG4iXSwibmFtZXMiOlsidXNlVHJhbnNsYXRpb24iLCJ1c2VMb2NhbGUiLCJpMThuIiwibGFuZ3VhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/utils/useLocale.ts\n");

/***/ }),

/***/ "./src/utils/utils.ts":
/*!****************************!*\
  !*** ./src/utils/utils.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isPresent: () => (/* binding */ isPresent),\n/* harmony export */   isProductionDeployment: () => (/* binding */ isProductionDeployment)\n/* harmony export */ });\n// TODO use `isDefined` instead of `isPresent`\nconst isPresent = (a)=>{\n    return a !== undefined && a !== null;\n};\nconst isServer = ()=>\"undefined\" === \"undefined\";\n// TODO replace by `useIsClient` hook from `usehooks-ts`\nconst isBrowser = ()=>!isServer();\nconst isProductionDeployment = ()=>\"dev\" === \"prod\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvdXRpbHMvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsOENBQThDO0FBQ3ZDLE1BQU1BLFlBQVksQ0FBSUM7SUFDM0IsT0FBT0EsTUFBTUMsYUFBYUQsTUFBTTtBQUNsQyxFQUFDO0FBRUQsTUFBTUUsV0FBVyxJQUFNLGdCQUFrQjtBQUV6Qyx3REFBd0Q7QUFDakQsTUFBTUMsWUFBWSxJQUFNLENBQUNELFdBQVU7QUFFbkMsTUFBTUUseUJBQXlCLElBQU1DLEtBQWtDLEtBQUssT0FBTSIsInNvdXJjZXMiOlsid2VicGFjazovL2JyYXRpc2xhdmEtbmV4dC8uL3NyYy91dGlscy91dGlscy50cz9hYWJlIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRPRE8gdXNlIGBpc0RlZmluZWRgIGluc3RlYWQgb2YgYGlzUHJlc2VudGBcbmV4cG9ydCBjb25zdCBpc1ByZXNlbnQgPSA8VT4oYTogVSB8IG51bGwgfCB1bmRlZmluZWQgfCB2b2lkKTogYSBpcyBVID0+IHtcbiAgcmV0dXJuIGEgIT09IHVuZGVmaW5lZCAmJiBhICE9PSBudWxsXG59XG5cbmNvbnN0IGlzU2VydmVyID0gKCkgPT4gdHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCdcblxuLy8gVE9ETyByZXBsYWNlIGJ5IGB1c2VJc0NsaWVudGAgaG9vayBmcm9tIGB1c2Vob29rcy10c2BcbmV4cG9ydCBjb25zdCBpc0Jyb3dzZXIgPSAoKSA9PiAhaXNTZXJ2ZXIoKVxuXG5leHBvcnQgY29uc3QgaXNQcm9kdWN0aW9uRGVwbG95bWVudCA9ICgpID0+IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0RFUExPWU1FTlQgPT09ICdwcm9kJ1xuIl0sIm5hbWVzIjpbImlzUHJlc2VudCIsImEiLCJ1bmRlZmluZWQiLCJpc1NlcnZlciIsImlzQnJvd3NlciIsImlzUHJvZHVjdGlvbkRlcGxveW1lbnQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfREVQTE9ZTUVOVCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/utils/utils.ts\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "@next/third-parties/google":
/*!*********************************************!*\
  !*** external "@next/third-parties/google" ***!
  \*********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@next/third-parties/google");

/***/ }),

/***/ "next-i18next":
/*!*******************************!*\
  !*** external "next-i18next" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-i18next");

/***/ }),

/***/ "next-plausible":
/*!*********************************!*\
  !*** external "next-plausible" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-plausible");

/***/ }),

/***/ "next-query-params":
/*!************************************!*\
  !*** external "next-query-params" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-query-params");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/error":
/*!*****************************!*\
  !*** external "next/error" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/error");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("styled-jsx/style");

/***/ }),

/***/ "use-query-params":
/*!***********************************!*\
  !*** external "use-query-params" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("use-query-params");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "@tanstack/react-query":
/*!****************************************!*\
  !*** external "@tanstack/react-query" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@tanstack/react-query");;

/***/ }),

/***/ "react-aria":
/*!*****************************!*\
  !*** external "react-aria" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-aria");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-vertical-timeline-component"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5C_error.jsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();