fragment ArticleSlugEntity on ArticleEntity {
  __typename
  id
  attributes {
    slug
    title
    locale
  }
}

fragment ArticleCardEntity on ArticleEntity {
  ...ArticleSlugEntity
  attributes {
    perex
    addedAt
    coverMedia {
      data {
        ...UploadImageEntity
      }
    }
    tag {
      data {
        ...TagEntity
      }
    }
  }
}

fragment ArticleEntity on ArticleEntity {
  ...ArticleCardEntity
  attributes {
    alias
    content
    files {
      ...FileBlock
    }
    gallery {
      data {
        ...UploadImageEntity
      }
    }
  }
}

query ArticleBySlug($slug: String!, $locale: I18NLocaleCode!) {
  articles(filters: { slug: { eq: $slug } }, locale: $locale) {
    data {
      ...ArticleEntity
    }
  }
}

query ArticlesStaticPaths($limit: Int = -1) {
  articles(locale: "all", sort: "addedAt:desc", pagination: { limit: $limit }) {
    data {
      ...ArticleSlugEntity
    }
  }
}

query Articles(
  $sort: [String]
  $limit: Int
  $start: Int
  $filters: ArticleFiltersInput
  $locale: I18NLocaleCode
) {
  articles(
    sort: $sort
    pagination: { limit: $limit, start: $start }
    filters: $filters
    locale: $locale
  ) {
    data {
      ...ArticleCardEntity
    }
  }
}

query ArticlesRssFeed($locale: I18NLocaleCode!) {
  articles(locale: $locale, sort: "addedAt:desc") {
    data {
      id
      attributes {
        slug
        title
        addedAt
        perex
        tag {
          data {
            attributes {
              title
              pageCategory {
                data {
                  attributes {
                    title
                  }
                }
              }
            }
          }
        }
        coverMedia {
          data {
            attributes {
              url
              mime
              size
            }
          }
        }
      }
    }
  }
}

query Dev_AllArticles(
  $sort: [String]
  $limit: Int
  $start: Int
  $filters: ArticleFiltersInput
  $locale: I18NLocaleCode
) {
  articles(
    sort: $sort
    pagination: { limit: $limit, start: $start }
    filters: $filters
    locale: $locale
  ) {
    data {
      ...ArticleEntity
    }
  }
}
