fragment UploadImageSrcEntity on UploadFileEntity {
  id
  attributes {
    url
  }
}

fragment UploadImageEntity on UploadFileEntity {
  id
  attributes {
    url
    width
    height
    caption
    alternativeText
    name
  }
}

fragment UploadFileEntity on UploadFileEntity {
  id
  attributes {
    url
    name
    ext
    size
    createdAt
    updatedAt
  }
}

fragment CommonLink on ComponentBlocksCommonLink {
  label
  page {
    data {
      ...PageSlugEntity
    }
  }
  article {
    data {
      ...ArticleSlugEntity
    }
  }
  url
  analyticsId
}

fragment PageLink on ComponentBlocksPageLink {
  label: title
  page {
    data {
      ...PageSlugEntity
    }
  }
  url
  analyticsId
}

fragment FooterColumnBlock on ComponentBlocksFooterColumn {
  title
  links {
    ...CommonLink
  }
}

fragment Footer on Footer {
  facebookUrl
  instagramUrl
  columns {
    ...FooterColumnBlock
  }
  accessibilityPageLink {
    ...CommonLink
  }
  innovationsLink {
    ...CommonLink
  }
  contactText
}

fragment MenuLink on ComponentMenuMenuLink {
  id
  label
  page {
    data {
      ...PageSlugEntity
    }
  }
  url
  analyticsId
}

fragment MenuSection on ComponentMenuMenuSection {
  id
  label
  icon
  page {
    data {
      ...PageSlugEntity
    }
  }
  links {
    ...MenuLink
  }
}

fragment MenuItem on ComponentMenuMenuItem {
  id
  label
  icon
  page {
    data {
      ...PageSlugEntity
    }
  }
  sections {
    ...MenuSection
  }
}

fragment HeaderLink on ComponentGeneralHeaderLink {
  label
  page {
    data {
      ...PageSlugEntity
    }
  }
  url
  analyticsId
  showOnDesktop
  showOnMobile
  icon
}

fragment GeneralPageRelation on PageEntity {
  id
  attributes {
    title
    slug
  }
}

fragment General on General {
  header {
    links {
      ...HeaderLink
    }
    accountLink {
      ...CommonLink
    }
  }
  newsPage {
    data {
      ...GeneralPageRelation
      ...PageParentPages
    }
  }
  officialBoardPage {
    data {
      ...GeneralPageRelation
    }
  }
  privacyPolicyPage {
    data {
      ...GeneralPageRelation
    }
  }
  vznPage {
    data {
      ...GeneralPageRelation
    }
  }
  inbaPage {
    data {
      ...GeneralPageRelation
    }
  }
  inbaReleasesPage {
    data {
      ...GeneralPageRelation
    }
  }
}

fragment Alert on Alert {
  updatedAt
  text
}

query General($locale: I18NLocaleCode!) {
  general(locale: $locale) {
    data {
      attributes {
        ...General
      }
    }
  }
  menu(locale: $locale) {
    data {
      attributes {
        menus {
          ...MenuItem
        }
      }
    }
  }
  footer(locale: $locale) {
    data {
      attributes {
        ...Footer
      }
    }
  }
  alert(locale: $locale) {
    data {
      attributes {
        ...Alert
      }
    }
  }
}

query Alert($locale: I18NLocaleCode!) {
  alert(locale: $locale) {
    data {
      attributes {
        ...Alert
      }
    }
  }
}
